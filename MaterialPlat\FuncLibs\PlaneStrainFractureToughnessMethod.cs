using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;

namespace TestExpert.PlaneStrainFractureToughnessMethod.Commons
{
    public class PlaneStrainFractureToughnessMethod
    {
        #region Const Field

        private const string DllName = "MetallicMaterials-PlaneStrainFractureToughnessMethod.dll";

        #endregion Const Field

        #region Methods

        /// <summary>
        /// 校验试样参数
        /// </summary>
        /// <param name="id">试样编号</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "CheckSpecimen", CallingConvention = CallingConvention.Cdecl)]
        public static extern int CheckSpecimen(string id);

        /// <summary>
        /// 初始化试样参数
        /// </summary>
        /// <param name="id">试样编号</param>
        /// <param name="identification">类别</param>
        /// <param name="width">宽度(mm)</param>
        /// <param name="thickness">厚度(mm)</param>
        /// <param name="notchLength">缺口长度(mm)</param>
        /// <param name="elasticModulus">弹性模量(GPa)</param>
        /// <param name="proofStrength">屈服强度(MPa)</param>
        /// <param name="span">跨距(mm)</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "InitializeSpecimen", CallingConvention = CallingConvention.Cdecl)]
        public static extern int InitializeSpecimen(string id, byte identification, double width, double thickness, double notchLength, double elasticModulus, double proofStrength, double span);

        /// <summary>
        /// 设置柔度系数
        /// </summary>
        /// <param name="c0">系数c0</param>
        /// <param name="c1">系数c1</param>
        /// <param name="c2">系数c2</param>
        /// <param name="c3">系数c3</param>
        /// <param name="c4">系数c4</param>
        /// <param name="c5">系数c5</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "SetCoefficient", CallingConvention = CallingConvention.Cdecl)]
        public static extern int SetCoefficient(double c0, double c1, double c2, double c3, double c4, double c5);

        /// <summary>
        /// 获取形状因子
        /// </summary>
        /// <param name="crackLength">裂纹长度(mm)</param>
        /// <param name="geometryFunction">形状因子</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "GetGeometryFunction", CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetGeometryFunction(double crackLength, out double geometryFunction);

        /// <summary>
        /// 获取应力强度因子
        /// </summary>
        /// <param name="force">力(kN)</param>
        /// <param name="crackLength">裂纹长度(mm)</param>
        /// <param name="stressIntensityFactor">应力强度因子(MPa·√m)</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "GetStressIntensityFactor", CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetStressIntensityFactor(double force, double crackLength, out double stressIntensityFactor);

        /// <summary>
        /// 获取力
        /// </summary>
        /// <param name="stressIntensityFactor">应力强度因子(MPa·√m)</param>
        /// <param name="crackLength">裂纹长度(mm)</param>
        /// <param name="force">力(kN)</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "GetForce", CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetForce(double stressIntensityFactor, double crackLength, out double force);

        /// <summary>
        /// 获取裂纹长度
        /// </summary>
        /// <param name="arrayExtension">变形数组</param>
        /// <param name="arrayLoad">载荷数组</param>
        /// <param name="size">数组长度</param>
        /// <param name="crackLength">裂纹长度(mm)</param>
        /// <param name="slope">柔度(mm/kN)</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "GetCrackLength", CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetCrackLength(double[] arrayExtension, double[] arrayLoad, int size, out double crackLength, out double slope);

        /// <summary>
        /// 根据裂纹长度获取弹性模量
        /// </summary>
        /// <param name="slope">柔度(mm/kN)</param>
        /// <param name="referenceModulus">参考弹性模量(GPa)</param>
        /// <param name="referenceCrackLength">参考裂纹长度(mm)</param>
        /// <param name="modulus">实际弹性模量(GPa)</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "ModulusFromCrackLength", CallingConvention = CallingConvention.Cdecl)]
        public static extern int ModulusFromCrackLength(double slope, double referenceModulus, double referenceCrackLength, out double modulus);

        /// <summary>
        /// 设置预制疲劳裂纹试验参数
        /// </summary>
        /// <param name="finalKmaxValue">终止应力强度因子</param>
        /// <param name="forceRatioValue">力值比</param>
        /// <param name="precrackLength">预裂纹长度</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "SetPrecrackingTestParameter", CallingConvention = CallingConvention.Cdecl)]
        public static extern int SetPrecrackingTestParameter(double finalKmaxValue, double forceRatioValue, double precrackLength);

        /// <summary>
        /// 设置预制疲劳裂纹试验初始最大应力强度因子
        /// </summary>
        /// <param name="initialKmaxValue">初始最大应力强度因子</param>
        /// <param name="crackLength">裂纹长度(mm)</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "SetPrecrackingInitialKmax", CallingConvention = CallingConvention.Cdecl)]
        public static extern int SetPrecrackingInitialKmax(double initialKmaxValue, double crackLength);

        /// <summary>
        /// 预制疲劳裂纹试验获取均幅值
        /// </summary>
        /// <param name="crackLength">裂纹长度(mm)</param>
        /// <param name="mean">均值(kN)</param>
        /// <param name="amplitude">幅值(kN)</param>
        /// <returns>0正常；非0为异常</returns>
        [DllImport(DllName, EntryPoint = "PrecrackingTestProcedure", CallingConvention = CallingConvention.Cdecl)]
        public static extern int PrecrackingTestProcedure(double crackLength, out double mean, out double amplitude);

        /// <summary>
        /// 最小二乘法线性拟合方法
        /// </summary>
        /// <param name="arrayX">x轴数组</param>
        /// <param name="arrayY">y轴数组</param>
        /// <param name="size">数组长度</param>
        /// <param name="slope">斜率</param>
        /// <param name="intercept">截距</param>
        /// <returns>0:正常；-1：异常</returns>
        [DllImport(DllName, EntryPoint = "LeastSquaresLinearFitting", CallingConvention = CallingConvention.Cdecl)]
        public static extern int LeastSquaresLinearFitting(double[] arrayX, double[] arrayY, int size, out double slope, out double intercept);

        /// <summary>
        /// 获取斜率
        /// </summary>
        /// <param name="loadList">力</param>
        /// <param name="extensionList">变形</param>
        /// <param name="upperLoad">线性拟合上限</param>
        /// <param name="lowerLoad">线性拟合下限</param>
        /// <param name="slope">斜率</param>
        /// <param name="intercept">截距</param>
        public static void GetSlope(List<double> loadList, List<double> extensionList, double upperLoad, double lowerLoad, out double slope, out double intercept)
        {
            int maxIndex = loadList.IndexOf(loadList.Max());
            int upperIndex = loadList.IndexOf(upperLoad);
            int lowerIndex = loadList.IndexOf(lowerLoad);

            loadList = loadList.Take(maxIndex + 1).ToList();
            loadList = loadList.Skip(lowerIndex).ToList();
            extensionList = extensionList.Take(maxIndex + 1).ToList();
            extensionList = extensionList.Skip(lowerIndex).ToList();

            loadList = loadList.Take(upperIndex + 1).ToList();
            extensionList = extensionList.Take(upperIndex + 1).ToList();
            LeastSquaresLinearFitting(extensionList.ToArray(), loadList.ToArray(), extensionList.Count, out slope, out intercept);
        }

        #endregion Methods
    }
}