import React, { useMemo } from 'react'
import { message } from 'antd'

import { updateInputVar } from '@/utils/services'
import useInputVariableByCode from '@/hooks/project/inputVariable/useInputVariableByCode'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

import Content from './content'

const Render = ({
    config: {
        attr: {
            tempRangeSettings,
            settingId
        } = {},
        variable: {
            temp,
            tempFluctuation,
            tempGradient
        } = {}
    },
    setConfig
}) => {
    const tempVari = useInputVariableByCode(temp?.code)
    const tempFluctuationVari = useInputVariableByCode(tempFluctuation?.code)
    const tempGradientVari = useInputVariableByCode(tempGradient?.code)

    const handleSettingChange = (setting) => {
        if (tempVari) {
            const record = setting.value.find(({ tempRange }) => {
                return !((tempRange.min > tempVari.default_val.value) || (tempVari.default_val.value > tempRange.max))
            })

            handleChangeValue(record)
        } else {
            message.error('未绑定试验温度')
        }
    }

    const handleChangeValue = async (record) => {
        if (record) {
            const { tempFluctuation: newTempFluctuation, tempGradient: newTempGradient } = record

            if (tempFluctuationVari) {
                await onChange({
                    ...tempFluctuationVari,
                    default_val: {
                        ...tempFluctuationVari.default_val,
                        value: newTempFluctuation
                    }
                })
            } else {
                message.error('未绑定温度波动')
            }

            if (tempGradientVari) {
                await onChange({
                    ...tempGradientVari,
                    default_val: {
                        ...tempGradientVari.default_val,
                        value: newTempGradient
                    }
                })
            } else {
                message.error('未绑定温度梯度')
            }
        }
    }

    const onChange = async (v) => {
        const res = await updateInputVar(v)

        if (res) {
            dispatchSyncInputVar({ code: v.code }, v)
        }
    }

    return (
        <Content
            tempRangeSettings={tempRangeSettings}
            handleSettingChange={handleSettingChange}
            settingId={settingId}
            setConfig={setConfig}
        />
    )
}

export default Render
