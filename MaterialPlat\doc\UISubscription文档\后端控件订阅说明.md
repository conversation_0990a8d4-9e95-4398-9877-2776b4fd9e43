# 前后端订阅机制说明

> 目的：向非研发介绍系统的前后端“订阅+取数”机制，覆盖接口职责、参数含义与优先级、数据获取流程。

## 1. 总览
- 订阅分两类：
  - 推送式（Subscription）：后端按周期/事件向前端推送数据（MessagePack 格式，通过系统总线发送）。
    - 自动推送
    - 手动推送（脚本推送）
  - 拉取式（GetData）：前端按需调用接口一次性获取数据（HTTP JSON 返回）。
- 数据源类型（dataSourceType / DataSourceType）：
  - daqbuffer（Buffer，环形缓存类输入变量）
  - doubleArray（二维数组）
  - doubleArraySet（二维数组集合）

## 2. 后端接口一览
接口基于控制器：api/template/uisubscription（UISubscriptionController）

1) 初始化订阅
- POST api/template/uisubscription/init
- Body: SubscriptionRequest（JSON）
- 返回：200 订阅成功 | 400 错误信息

2) 更新订阅
- POST api/template/uisubscription/update
- Body: SubscriptionRequest（JSON）
- 重新初始化订阅

3) 暂停/恢复/关闭订阅（按控件ID）
- POST api/template/uisubscription/pause
- POST api/template/uisubscription/resume
- POST api/template/uisubscription/close
- Body: 字符串 ControlCompId（作为 JSON 字符串传，如 "comp-1"）

4) 批量关闭某模板的所有订阅
- POST api/template/uisubscription/close-template
- Body: 字符串 TemplateName

5) 拉取式数据（一次性）
- POST api/template/uisubscription/getData
- Body: GetDataRequest（JSON）
- 返回：ReturnObjectData（doubleArray 系列为对象数组，buffer 也按对象数组组织）

## 3. 关键数据模型（简版）
- SubscriptionRequest
  - TemplateName: 模板名
  - ControlCompId: 控件ID
  - DataSourceType: 数据源类型（daqbuffer/doubleArray/doubleArraySet）
  - DataSourceCode: 输入变量Code
  - DataCodes: 需要的列（信号）集合
  - Timer: 定时器间隔（毫秒）；<=0 表示立即触发一次，不走周期
  - Number: 每次推送的数据点数量；>0 表示只取最新N条；<=0 表示不限定
  - TestStatus: 试验状态，0=历史（只推历史并暂停），1=实时（含历史+实时）
  - DaqCurveSelectedSampleCodes: 仅 buffer 历史分页时会遍历的试样列表

- GetDataRequest
  - TemplateName, dataSourceType, dataSourceCode, DataCodes
  - startIndex, endIndex（包含端点；doubleArray 中使用 Skip(start).Take(end-start+1)）
  - SampleCode（buffer 历史表名由 SampleCode+dataSourceCode 组成）
  - doubleArrayIndex（doubleArraySet 指定具体数组）

- 服务端推送给前端的消息结构（MessagePack）：
  - Buffer: SubscriptionTOUIBufferData { mode, sampleCode, controlCompId, data }
  - DoubleArray: SubscriptionTOUIDoubleAyyayData { mode, sampleCode, doubleArrayIndex, controlCompId, totalCount, data }
  - mode 语义：0 全量刷新（清空控件后重绘）；1 增量追加；2 开始；3 结束（开始/结束用在历史数据时，为了给前端标名什么时候推送结束）

## 4. 参数优先级与行为规则

- 优先级（从高到低）：
  1) Number > 0 优先：总是取“最新N条”并持续实时推送，忽略 TestStatus 的历史行为。
  2) TestStatus 决定历史与实时：
     - TestStatus = 0（历史态）：仅推历史数据（见流程A），推完后暂停；恢复时不触发历史补偿。
     - TestStatus = 1（实时态）：先补历史，再转实时流（见流程B）。
  3) Timer：>0 则周期拉取/推送；<=0 则立即触发一次，不走周期（DoubleArray 在 Timer<0 会触发一次并注册回调）。
- 自动补列：当 TestStatus=0（历史）且 DataCodes 中不含 "index" 时，会自动追加 "index" 列用于历史数据轴索引。
- 多试样：历史阶段会对 DaqCurveSelectedSampleCodes 逐一分页读取并推送（每页最大 MAXNUMBER=500000；单次推送切片上限 MAXPUSH=100000）。
- 脚本推送（doubleArray）
  - 脚本推送：Timer <= 0时，前端调用 SendValueToUI(data)，可以触发全量推送给UI刷新数据（用于可编辑表格）。
  - 脚本清空：前端调用 ClearDoubleArrayData()，手动清空前端数据，用于前端数据的立即清理
    - 表格的话前端会立即重新获取本页数据。
    - 曲线
## 5. 数据获取流程（核心）
### 5.1 buffer
A) buffer + 历史态（TestStatus=0，Number<=0）
- 启动即下发 mode=2（开始）。
- 分页从数据库读取 SampleCode+DataSourceCode 的历史记录，转换成 { DataCode -> double[] } 逐段推送：
  - 第一段以 mode=0（全量）；后续段以 mode=1（增量）。
- 推完历史后下发 mode=3（结束），并暂停订阅（不再实时追踪）。

B) buffer + 实时态（TestStatus=1，Number<=0，重点）
- 首次周期：先补历史（若有）：
  - 对每个试样 SampleCode 读取历史总数 count。
  - 若 count>0：分页读取（每页 MAXNUMBER），每段通过 PushData 切片（MAXPUSH）推给前端。
  - 若 count=0：直接使用当前内存 buffer 的实时数据作为首次数据（有些高频临时数据不入库）。
  - 记录 lastCount（数据库已推送条数）与 lastTail（内存环形尾指针）。
- 后续周期：转为 GetRealTimeData 增量拉取内存新增数据（按环形缓存 tail 指针计算 TopIndex→newTail 区间），以 mode=1 推送。
- 恢复场景：若暂停后恢复，优先用数据库 lastCount→newCount 补历史增量，若数据库没有则回退到内存实时；补完后清除 _isResume 标记，继续实时。

C) buffer + Number>0（无论 TestStatus）
- 每次周期仅取内存中“最新N条”推送（mode 由是否首次推送决定），不进行数据库历史阶段。

### 5.2 doubleArray / doubleArraySet

- Timer <= 0：立即触发一次 SendToUICallback 全量推送，并将 TestStatus 设为 0（一次性全量刷新），随后注册回调由用户调用SendToUI刷新。
- Timer > 0：按周期执行 OnTimerCallback：
  - Number>0：推最新N条；
  - TestStatus=0：一次性全量然后暂停；
  - TestStatus=1：按增量机制（由 DoubleArraySubscription 内部逻辑与回调决定）。
- 如果用户调用SendClearDataCmdToUI()，会下发 mode=0 空数据将前端数据清空，通知前端刷新数据。
- 拉取式 getData：
  - doubleArray：按列取值，范围 [startIndex, endIndex]（含端点），totalCount=当前行数。
  - doubleArraySet：先索引到具体 DoubleArray 再按 doubleArray 逻辑。
  - daqbuffer：走数据库 OnlyDouble 查询并组装，totalCount 由数据库 count 获取。

## 6. 示例

1) 初始化订阅（buffer，实时态，Number=-1）
```
POST /api/template/uisubscription/init
Content-Type: application/json
{
  "TemplateName": "Tmpl-1",
  "ControlCompId": "curve-1",
  "DataSourceType": "daqbuffer",
  "DataSourceCode": "buf_v1",
  "DataCodes": ["time", "force", "disp"],
  "Timer": 100,
  "Number": -1,
  "TestStatus": 1,
  "DaqCurveSelectedSampleCodes": ["S1"]
}
```
行为：
- 首次周期：先从 DB(S1+buf_v1) 读取历史（若有，分页→切片推送）；若无则直接取当前内存数据；
- 后续周期：按内存环形缓存增量推送。

2) 初始化订阅（buffer，历史态，一次性）
```
{
  "TemplateName": "Tmpl-1",
  "ControlCompId": "curve-1",
  "DataSourceType": "daqbuffer",
  "DataSourceCode": "buf_v1",
  "DataCodes": ["time", "force"],
  "Timer": 0,
  "Number": -1,
  "TestStatus": 0,
  "DaqCurveSelectedSampleCodes": ["S1","S2"]
}
```
行为：
- 自动添加 DataCodes: ["time","force","index"]；
- 依次对 S1、S2 分页读取历史，mode=0/1 推完后发送 mode=3 并暂停。

3) 拉取式 getData（doubleArray 范围查询）
```
POST /api/template/uisubscription/getData
{
  "TemplateName": "Tmpl-1",
  "dataSourceType": "doubleArray",
  "dataSourceCode": "arr_1",
  "DataCodes": ["t","x"],
  "startIndex": 1000,
  "endIndex": 1999
}
```
返回：
- totalCount = 当前行数；data.t/data.x 为 1000~1999 共 1000 条。

## 7. 前端接入要点
- 订阅消息解码：MessagePack → SubscriptionTOUIBufferData / SubscriptionTOUIDoubleAyyayData。
- 根据 mode：
  - 0 清空并全量刷新；1 追加；2/3 可用于显示“加载历史中/完成”。
- 多试样时 sampleCode 区分数据归属；doubleArraySet 用 doubleArrayIndex 区分集合内数组。
- 控制接口 pause/resume/close 需传 JSON 字符串（例如 "curve-1"）。

## 8. 常见问题与注意事项
- Number 与 TestStatus 冲突时：Number>0 优先（仅取最新N条，不做历史阶段）。
- 历史阶段若 count=0，会直接取内存数据作为首次包。
- 大数据推送安全：MAXNUMBER=500000（DB分页）；MAXPUSH=100000（切片推送），防止前端一次性承压过大。
- 历史态自动追加 "index"：前端无需显式传入。

—— 完 ——


