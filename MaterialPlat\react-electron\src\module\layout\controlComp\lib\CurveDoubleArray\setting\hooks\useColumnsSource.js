import React, { useMemo } from 'react'
import { useSelector, useDispatch } from 'react-redux'

import useDoubleArrayInputVariable from '@/hooks/project/inputVariable/useDoubleArrayInputVariable'
import useDoubleArrayListInputVariable from '@/hooks/project/inputVariable/useDoubleArrayListInputVariable'
import useBufferInputVariable from '@/hooks/project/inputVariable/useBufferInputVariable'

import { SOURCE_TYPE } from '../constants'
import { getColumnsSource } from '../../arrayUtils/geColumnsSource'
import { findUnitListByDimensionId } from '../../arrayUtils/unit'

const useColumnsSource = ({ sourceType, sourceInputCode, isBufferCurve }) => {
    const inputVariableDoubleArray = useDoubleArrayInputVariable()
    const inputVariableDoubleArrayList = useDoubleArrayListInputVariable()
    const inputVariableBuffer = useBufferInputVariable()
    const signalListData = useSelector(state => state.template.signalList)

    const channels = useMemo(() => {
        if (!sourceInputCode) {
            return []
        }

        if (isBufferCurve) {
            const bufferVar = inputVariableBuffer.find(i => i.code === sourceInputCode)
            if (!bufferVar) {
                return []
            }

            const res = bufferVar?.buffer_tab?.signals?.map(c => {
                const signal = signalListData.find(f => f.code === c)

                const unitList = findUnitListByDimensionId(signal?.dimension_id)

                return {
                    code: c,
                    name: signal?.variable_name ?? c,
                    dimensionId: signal?.dimension_id ?? '',
                    unitId: signal?.unit_id ?? '',
                    unitList
                }
            })

            return res ?? []
        }

        return getColumnsSource({ sourceType, sourceInputCode })
    }, [
        sourceType,
        sourceInputCode,
        inputVariableDoubleArray,
        inputVariableDoubleArrayList
    ])

    return channels
}

export default useColumnsSource
