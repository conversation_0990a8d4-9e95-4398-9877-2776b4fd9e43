import store from '@/redux/store/index'

import { SOURCE_TYPE } from '../constants/constants'
import { findUnitListByDimensionId } from './unit'

const getColumnsSource = ({ sourceType, sourceInputCode }) => {
    const { inputVariableMap } = store.getState().inputVariable

    let columns = []

    if (sourceType === SOURCE_TYPE.单数据源) {
        const selectedDoubleArrayVariable = inputVariableMap.get(sourceInputCode)

        columns = selectedDoubleArrayVariable?.double_array_tab?.columns ?? []
    }

    if (sourceType === SOURCE_TYPE.多数据源) {
        const doubleArrayList = inputVariableMap.get(sourceInputCode)
        const selectedDoubleArrayVariable = inputVariableMap.get(doubleArrayList?.double_array_list_tab?.dataSourceCode)

        columns = selectedDoubleArrayVariable?.double_array_tab?.columns ?? []
    }

    const res = columns.filter(c => c.type === 'Number').map(c => {
        const unitList = findUnitListByDimensionId(c?.typeParam?.unitId)

        return {
            code: c.code,
            name: c.showName,
            dimensionId: c?.typeParam?.dimensionId,
            unitId: c?.typeParam?.unitId,
            unitList
        }
    })

    return res
}

export { getColumnsSource }
