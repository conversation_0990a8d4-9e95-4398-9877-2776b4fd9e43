[{:dependencies {com.cognitect/transit-java {:vsn "0.8.337", :native-prefix nil}, metosin/reitit-middleware {:vsn "0.5.18", :native-prefix nil}, selmer {:vsn "1.12.55", :native-prefix nil}, args4j {:vsn "2.0.26", :native-prefix nil}, com.github.houbb/pinyin {:vsn "0.3.1", :native-prefix nil}, org.bouncycastle/bcprov-jdk15on {:vsn "1.62", :native-prefix nil}, buddy/buddy-core {:vsn "1.6.0", :native-prefix nil}, org.clojure/data.json {:vsn "0.2.6", :native-prefix nil}, luminus-jetty {:vsn "0.2.3", :native-prefix nil}, org.lz4/lz4-java {:vsn "1.8.0", :native-prefix nil}, org.clojure/clojure {:vsn "1.12.0", :native-prefix nil}, lambdaisland/deep-diff2 {:vsn "2.10.211", :native-prefix nil}, primitive-math {:vsn "0.1.6", :native-prefix nil}, io.github.classgraph/classgraph {:vsn "4.8.139", :native-prefix nil}, com.googlecode.log4jdbc/log4jdbc {:vsn "1.2", :native-prefix nil}, mount {:vsn "0.1.17", :native-prefix nil}, org.clojure/tools.analyzer {:vsn "1.1.0", :native-prefix nil}, org.eclipse.jetty/jetty-xml {:vsn "10.0.7", :native-prefix nil}, com.zaxxer/HikariCP {:vsn "4.0.3", :native-prefix nil}, clojure.java-time {:vsn "1.1.0", :native-prefix nil}, org.eclipse.jetty/jetty-servlet {:vsn "10.0.7", :native-prefix nil}, org.apache.poi/poi {:vsn "4.1.2", :native-prefix nil}, org.apache.commons/commons-compress {:vsn "1.19", :native-prefix nil}, ring/ring-devel {:vsn "1.9.6", :native-prefix nil}, com.google.errorprone/error_prone_annotations {:vsn "2.0.18", :native-prefix nil}, io.prometheus/simpleclient {:vsn "0.12.0", :native-prefix nil}, org.apache.commons/commons-lang3 {:vsn "3.11", :native-prefix nil}, org.clojure/tools.logging {:vsn "1.2.4", :native-prefix nil}, org.nrepl/incomplete {:vsn "0.1.0", :native-prefix nil}, metosin/ring-http-response {:vsn "0.9.3", :native-prefix nil}, org.clojure/core.specs.alpha {:vsn "0.4.74", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-jetty-api {:vsn "10.0.7", :native-prefix nil}, org.tukaani/xz {:vsn "1.9", :native-prefix nil}, conman {:vsn "0.9.5", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-jetty-common {:vsn "10.0.7", :native-prefix nil}, ns-tracker {:vsn "0.4.0", :native-prefix nil}, hikari-cp {:vsn "2.14.0", :native-prefix nil}, org.bouncycastle/bcpkix-jdk15on {:vsn "1.62", :native-prefix nil}, metosin/muuntaja {:vsn "0.6.8", :native-prefix nil}, io.netty/netty-common {:vsn "4.1.25.Final", :native-prefix nil}, lambdaisland/kaocha {:vsn "1.87.1366", :native-prefix nil}, org.apiguardian/apiguardian-api {:vsn "1.0.0", :native-prefix nil}, org.zeromq/jeromq {:vsn "0.4.3", :native-prefix nil}, metosin/ring-swagger-ui {:vsn "4.3.0", :native-prefix nil}, org.apache.poi/poi-ooxml-schemas {:vsn "4.1.2", :native-prefix nil}, com.fasterxml.jackson.core/jackson-databind {:vsn "********", :native-prefix nil}, byte-streams {:vsn "0.2.4", :native-prefix nil}, org.clojure/spec.alpha {:vsn "0.5.238", :native-prefix nil}, org.clojure/tools.cli {:vsn "1.0.214", :native-prefix nil}, clj-stacktrace {:vsn "0.2.8", :native-prefix nil}, org.eclipse.jetty.http2/http2-server {:vsn "10.0.7", :native-prefix nil}, alkie/datascope {:vsn "0.1.2", :native-prefix nil}, clj-time {:vsn "0.14.3", :native-prefix nil}, mvxcvi/puget {:vsn "1.1.2", :native-prefix nil}, com.github.houbb/nlp-common {:vsn "0.0.3", :native-prefix nil}, expiring-map {:vsn "0.1.9", :native-prefix nil}, ring/ring-ssl {:vsn "0.3.0", :native-prefix nil}, crypto-random {:vsn "1.2.0", :native-prefix nil}, org.codehaus.mojo/animal-sniffer-annotations {:vsn "1.14", :native-prefix nil}, lambdaisland/clj-diff {:vsn "1.4.78", :native-prefix nil}, jakarta.transaction/jakarta.transaction-api {:vsn "1.3.3", :native-prefix nil}, org.eclipse.jetty/jetty-http {:vsn "10.0.7", :native-prefix nil}, org.eclipse.jetty/jetty-util {:vsn "10.0.7", :native-prefix nil}, com.taoensso/encore {:vsn "3.31.0", :native-prefix nil}, metosin/spec-tools {:vsn "0.10.5", :native-prefix nil}, compojure {:vsn "1.6.2", :native-prefix nil}, org.clojure/tools.analyzer.jvm {:vsn "1.2.2", :native-prefix nil}, io.netty/netty-handler-proxy {:vsn "4.1.25.Final", :native-prefix nil}, jarohen/chime {:vsn "0.3.4-20210630.135849-1", :native-prefix nil}, riddley {:vsn "0.1.12", :native-prefix nil}, com.github.houbb/heaven {:vsn "0.1.154", :native-prefix nil}, net.incongru.watchservice/barbary-watchservice {:vsn "1.0", :native-prefix nil}, commons-fileupload {:vsn "1.3.3", :native-prefix nil}, camel-snake-kebab {:vsn "0.4.2", :native-prefix nil}, lambdaisland/kaocha-cloverage {:vsn "1.1.89", :native-prefix nil}, io.prometheus/simpleclient_tracer_otel {:vsn "0.12.0", :native-prefix nil}, com.layerware/hugsql-adapter {:vsn "0.5.3", :native-prefix nil}, io.netty/netty-codec {:vsn "4.1.25.Final", :native-prefix nil}, prone {:vsn "2021-04-23", :native-prefix nil}, walmartlabs/system-viz {:vsn "0.4.0", :native-prefix nil}, org.apache.commons/commons-math3 {:vsn "3.6.1", :native-prefix nil}, org.clojure/tools.macro {:vsn "0.1.5", :native-prefix nil}, org.zeromq/jnacl {:vsn "0.1.0", :native-prefix nil}, org.eclipse.jetty/jetty-annotations {:vsn "10.0.7", :native-prefix nil}, lambdaisland/deep-diff {:vsn "0.0-47", :native-prefix nil}, realize {:vsn "2019-04-24", :native-prefix nil}, com.fasterxml.jackson.dataformat/jackson-dataformat-cbor {:vsn "2.9.6", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-core-common {:vsn "10.0.7", :native-prefix nil}, com.layerware/hugsql-core {:vsn "0.5.3", :native-prefix nil}, com.googlecode.json-simple/json-simple {:vsn "1.1.1", :native-prefix nil}, org.iq80.snappy/snappy {:vsn "0.4", :native-prefix nil}, ring/ring-defaults {:vsn "0.3.2", :native-prefix nil}, com.cognitect/transit-cljs {:vsn "0.8.256", :native-prefix nil}, org.clojure/google-closure-library {:vsn "0.0-20170809-b9c14c6b", :native-prefix nil}, io.aviso/pretty {:vsn "1.3", :native-prefix nil}, cprop {:vsn "0.1.19", :native-prefix nil}, fipp {:vsn "0.6.25", :native-prefix nil}, io.netty/netty-codec-socks {:vsn "4.1.25.Final", :native-prefix nil}, luminus-migrations {:vsn "0.7.5", :native-prefix nil}, ch.qos.logback/logback-core {:vsn "1.2.3", :native-prefix nil}, org.clojure/clojurescript {:vsn "1.10.520", :native-prefix nil}, io.netty/netty-buffer {:vsn "4.1.25.Final", :native-prefix nil}, aero {:vsn "1.1.6", :native-prefix nil}, io.prometheus/simpleclient_tracer_common {:vsn "0.12.0", :native-prefix nil}, com.stuartsierra/component {:vsn "0.3.2", :native-prefix nil}, io.netty/netty-handler {:vsn "4.1.25.Final", :native-prefix nil}, org.flatland/ordered {:vsn "1.5.9", :native-prefix nil}, luminus/ring-ttl-session {:vsn "0.3.3", :native-prefix nil}, medley {:vsn "1.3.0", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-core-server {:vsn "10.0.7", :native-prefix nil}, org.clojure/tools.namespace {:vsn "1.3.0", :native-prefix nil}, org.webjars/webjars-locator-core {:vsn "0.50", :native-prefix nil}, com.google.jsinterop/jsinterop-annotations {:vsn "1.0.0", :native-prefix nil}, rhizome {:vsn "0.2.9", :native-prefix nil}, org.eclipse.jetty.http2/http2-hpack {:vsn "10.0.7", :native-prefix nil}, org.eclipse.jetty/jetty-plus {:vsn "10.0.7", :native-prefix nil}, luminus-transit {:vsn "0.1.5", :native-prefix nil}, com.nextjournal/beholder {:vsn "1.0.2", :native-prefix nil}, metosin/reitit-swagger {:vsn "0.5.18", :native-prefix nil}, com.github.spullara.mustache.java/compiler {:vsn "0.9.10", :native-prefix nil}, eidolon {:vsn "0.2.0", :native-prefix nil}, com.fasterxml.jackson.core/jackson-core {:vsn "2.13.2", :native-prefix nil}, slingshot {:vsn "0.12.2", :native-prefix nil}, org.yaml/snakeyaml {:vsn "1.31", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-servlet {:vsn "10.0.7", :native-prefix nil}, org.tobereplaced/lettercase {:vsn "1.0.0", :native-prefix nil}, org.ow2.asm/asm {:vsn "9.2", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-jetty-server {:vsn "10.0.7", :native-prefix nil}, metosin/reitit-ring {:vsn "0.5.18", :native-prefix nil}, cheshire {:vsn "5.8.1", :native-prefix nil}, org.apache.poi/poi-scratchpad {:vsn "4.1.2", :native-prefix nil}, lambdaisland/tools.namespace {:vsn "0.3.256", :native-prefix nil}, meta-merge {:vsn "1.0.0", :native-prefix nil}, org.apache.httpcomponents/httpcore {:vsn "4.4.5", :native-prefix nil}, org.eclipse.jetty/jetty-security {:vsn "10.0.7", :native-prefix nil}, markdown-clj {:vsn "1.11.3", :native-prefix nil}, mvxcvi/arrangement {:vsn "2.1.0", :native-prefix nil}, potemkin {:vsn "0.4.5", :native-prefix nil}, jakarta.annotation/jakarta.annotation-api {:vsn "1.3.5", :native-prefix nil}, clj-wallhack {:vsn "1.0.1", :native-prefix nil}, hawk {:vsn "0.2.11", :native-prefix nil}, io.djy/ezzmq {:vsn "0.8.2", :native-prefix nil}, lambdaisland/kaocha-junit-xml {:vsn "1.17.101", :native-prefix nil}, com.cognitect/transit-js {:vsn "0.8.846", :native-prefix nil}, io.methvin/directory-watcher {:vsn "0.17.3", :native-prefix nil}, progrock {:vsn "0.1.2", :native-prefix nil}, metosin/reitit-core {:vsn "0.5.18", :native-prefix nil}, org.mozilla/rhino {:vsn "1.7R5", :native-prefix nil}, com.taoensso/truss {:vsn "1.6.0", :native-prefix nil}, crypto-equality {:vsn "1.0.0", :native-prefix nil}, funcool/cuerdas {:vsn "2.2.0", :native-prefix nil}, ring/ring-headers {:vsn "0.3.0", :native-prefix nil}, org.clojure/google-closure-library-third-party {:vsn "0.0-20170809-b9c14c6b", :native-prefix nil}, com.fasterxml.jackson.core/jackson-annotations {:vsn "2.13.2", :native-prefix nil}, pjstadig/humane-test-output {:vsn "0.11.0", :native-prefix nil}, io.prometheus/simpleclient_hotspot {:vsn "0.12.0", :native-prefix nil}, io.aleph/dirigiste {:vsn "0.1.6-alpha1", :native-prefix nil}, org.apache.xmlbeans/xmlbeans {:vsn "3.1.0", :native-prefix nil}, dk.ative/docjure {:vsn "1.14.0", :native-prefix nil}, org.apache.poi/poi-ooxml {:vsn "4.1.2", :native-prefix nil}, ring-cors {:vsn "0.1.13", :native-prefix nil}, org.webjars/webjars-locator-jboss-vfs {:vsn "0.1.0", :native-prefix nil}, com.google.javascript/closure-compiler-externs {:vsn "v20180805", :native-prefix nil}, org.apache.commons/commons-text {:vsn "1.9", :native-prefix nil}, org.javassist/javassist {:vsn "3.18.1-GA", :native-prefix nil}, org.eclipse.jetty/jetty-webapp {:vsn "10.0.7", :native-prefix nil}, org.clojure/java.classpath {:vsn "1.0.0", :native-prefix nil}, io.prometheus/simpleclient_pushgateway {:vsn "0.12.0", :native-prefix nil}, org.eclipse.jetty/jetty-alpn-java-server {:vsn "10.0.7", :native-prefix nil}, commons-codec {:vsn "1.10", :native-prefix nil}, com.google.guava/guava {:vsn "25.1-jre", :native-prefix nil}, org.clojure/data.xml {:vsn "0.0.8", :native-prefix nil}, json-html {:vsn "0.4.7", :native-prefix nil}, cloverage {:vsn "1.2.4", :native-prefix nil}, metosin/reitit-schema {:vsn "0.5.18", :native-prefix nil}, borkdude/dynaload {:vsn "0.2.2", :native-prefix nil}, adzerk/boot-test {:vsn "1.2.0", :native-prefix nil}, org.webjars/webjars-locator {:vsn "0.45", :native-prefix nil}, org.eclipse.jetty.http2/http2-common {:vsn "10.0.7", :native-prefix nil}, org.msgpack/msgpack {:vsn "0.6.12", :native-prefix nil}, commons-logging {:vsn "1.2", :native-prefix nil}, borkdude/edamame {:vsn "0.0.19", :native-prefix nil}, com.google.j2objc/j2objc-annotations {:vsn "1.1", :native-prefix nil}, org.clojure/data.csv {:vsn "1.1.0", :native-prefix nil}, com.taoensso/timbre {:vsn "6.0.1", :native-prefix nil}, io.prometheus/simpleclient_tracer_otel_agent {:vsn "0.12.0", :native-prefix nil}, metosin/malli {:vsn "0.8.2", :native-prefix nil}, com.cognitect/transit-clj {:vsn "0.8.319", :native-prefix nil}, com.github.seancorfield/next.jdbc {:vsn "1.2.796", :native-prefix nil}, io.netty/netty-transport {:vsn "4.1.25.Final", :native-prefix nil}, com.github.oshi/oshi-core {:vsn "6.6.6", :native-prefix nil}, org.ow2.asm/asm-commons {:vsn "9.2", :native-prefix nil}, hiccup {:vsn "1.0.5", :native-prefix nil}, io.netty/netty-transport-native-unix-common {:vsn "4.1.25.Final", :native-prefix nil}, digest {:vsn "1.4.7", :native-prefix nil}, io.netty/netty-codec-dns {:vsn "4.1.25.Final", :native-prefix nil}, clj-commons/clj-yaml {:vsn "0.7.109", :native-prefix nil}, io.netty/netty-codec-http {:vsn "4.1.25.Final", :native-prefix nil}, migratus {:vsn "1.4.2", :native-prefix nil}, ring/ring-codec {:vsn "1.1.1", :native-prefix nil}, metosin/reitit-malli {:vsn "0.5.18", :native-prefix nil}, org.apache.httpcomponents/httpclient {:vsn "4.5.2", :native-prefix nil}, ring-webjars {:vsn "0.2.0", :native-prefix nil}, metosin/schema-tools {:vsn "0.12.3", :native-prefix nil}, org.clojure/core.rrb-vector {:vsn "0.1.2", :native-prefix nil}, ring/ring-anti-forgery {:vsn "1.3.0", :native-prefix nil}, prismatic/schema {:vsn "1.1.12", :native-prefix nil}, org.webjars.npm/material-icons {:vsn "1.10.8", :native-prefix nil}, com.bhauman/spell-spec {:vsn "0.1.2", :native-prefix nil}, manifold {:vsn "0.1.9-alpha4", :native-prefix nil}, com.layerware/hugsql-adapter-next-jdbc {:vsn "0.5.3", :native-prefix nil}, instaparse {:vsn "1.4.8", :native-prefix nil}, org.checkerframework/checker-qual {:vsn "2.0.0", :native-prefix nil}, clout {:vsn "2.2.1", :native-prefix nil}, net.java.dev.jna/jna {:vsn "5.16.0", :native-prefix nil}, org.apache.poi/ooxml-schemas {:vsn "1.4", :native-prefix nil}, metosin/reitit-sieppari {:vsn "0.5.18", :native-prefix nil}, com.zaxxer/SparseBitSet {:vsn "1.2", :native-prefix nil}, com.andrewmcveigh/cljs-time {:vsn "0.5.2", :native-prefix nil}, org.eclipse.jetty/jetty-jndi {:vsn "10.0.7", :native-prefix nil}, org.clojure/core.match {:vsn "1.0.0", :native-prefix nil}, org.eclipse.jetty/jetty-io {:vsn "10.0.7", :native-prefix nil}, org.clojure/tools.reader {:vsn "1.3.6", :native-prefix nil}, io.prometheus/simpleclient_common {:vsn "0.12.0", :native-prefix nil}, joda-time {:vsn "2.9.9", :native-prefix nil}, org.tcrawley/dynapath {:vsn "1.1.0", :native-prefix nil}, nrepl {:vsn "1.0.0", :native-prefix nil}, tigris {:vsn "0.1.1", :native-prefix nil}, clj-commons/iapetos {:vsn "0.1.14", :native-prefix nil}, org.eclipse.jetty.toolchain/jetty-servlet-api {:vsn "4.0.6", :native-prefix nil}, net.java.dev.jna/jna-platform {:vsn "5.16.0", :native-prefix nil}, com.taoensso/nippy {:vsn "3.2.0", :native-prefix nil}, clj-http {:vsn "2.3.0", :native-prefix nil}, javax.servlet/javax.servlet-api {:vsn "3.1.0", :native-prefix nil}, net.clojars.zhaoyul/monitor-lib {:vsn "0.1.2-20250729.055020-4", :native-prefix nil}, metosin/reitit-http {:vsn "0.5.18", :native-prefix nil}, info.sunng/ring-jetty9-adapter {:vsn "0.17.2", :native-prefix nil}, buddy/buddy-auth {:vsn "2.2.0", :native-prefix nil}, org.apache.commons/commons-collections4 {:vsn "4.4", :native-prefix nil}, io.netty/netty-resolver {:vsn "4.1.25.Final", :native-prefix nil}, com.rpl/specter {:vsn "1.1.4", :native-prefix nil}, io.netty/netty-transport-native-epoll {:vsn "4.1.25.Final", :native-prefix nil}, org.xerial/sqlite-jdbc {:vsn "********", :native-prefix nil}, org.webjars.npm/bulma {:vsn "0.9.4", :native-prefix nil}, org.slf4j/slf4j-api {:vsn "1.7.25", :native-prefix nil}, walmartlabs/datascope {:vsn "0.1.1", :native-prefix nil}, expound {:vsn "0.9.0", :native-prefix nil}, buddy/buddy-sign {:vsn "3.1.0", :native-prefix nil}, com.google.javascript/closure-compiler-unshaded {:vsn "v20180805", :native-prefix nil}, org.clojure/test.check {:vsn "1.1.1", :native-prefix nil}, metosin/reitit-frontend {:vsn "0.5.18", :native-prefix nil}, io.netty/netty-resolver-dns {:vsn "4.1.25.Final", :native-prefix nil}, org.ow2.asm/asm-analysis {:vsn "9.2", :native-prefix nil}, funcool/struct {:vsn "1.4.0", :native-prefix nil}, com.github.virtuald/curvesapi {:vsn "1.06", :native-prefix nil}, metosin/reitit-spec {:vsn "0.5.18", :native-prefix nil}, clj-tuple {:vsn "0.2.2", :native-prefix nil}, metosin/jsonista {:vsn "0.3.1", :native-prefix nil}, com.google.protobuf/protobuf-java {:vsn "3.0.2", :native-prefix nil}, com.carouselapps/to-jdbc-uri {:vsn "0.5.0", :native-prefix nil}, metosin/reitit-swagger-ui {:vsn "0.5.18", :native-prefix nil}, metosin/reitit {:vsn "0.5.18", :native-prefix nil}, babashka/fs {:vsn "0.5.24", :native-prefix nil}, ring/ring-servlet {:vsn "1.9.4", :native-prefix nil}, ch.qos.logback/logback-classic {:vsn "1.2.3", :native-prefix nil}, org.clojure/core.memoize {:vsn "1.0.253", :native-prefix nil}, com.stuartsierra/dependency {:vsn "0.2.0", :native-prefix nil}, tech.droit/clj-diff {:vsn "1.0.1", :native-prefix nil}, net.i2p.crypto/eddsa {:vsn "0.3.0", :native-prefix nil}, dorothy {:vsn "0.0.6", :native-prefix nil}, org.clojure/data.priority-map {:vsn "1.1.0", :native-prefix nil}, org.clojure/java.data {:vsn "1.0.95", :native-prefix nil}, ring/ring-mock {:vsn "0.4.0", :native-prefix nil}, org.eclipse.jetty/jetty-server {:vsn "10.0.7", :native-prefix nil}, commons-io {:vsn "2.6", :native-prefix nil}, io.netty/netty-all {:vsn "4.1.68.Final", :native-prefix nil}, org.apache.httpcomponents/httpmime {:vsn "4.5.2", :native-prefix nil}, com.google.code.findbugs/jsr305 {:vsn "3.0.1", :native-prefix nil}, metosin/reitit-dev {:vsn "0.5.18", :native-prefix nil}, ring/ring-core {:vsn "1.7.1", :native-prefix nil}, org.clojure/core.cache {:vsn "1.0.225", :native-prefix nil}, org.ow2.asm/asm-tree {:vsn "9.2", :native-prefix nil}, org.clojure/core.async {:vsn "1.6.673", :native-prefix nil}, com.fasterxml.jackson.dataformat/jackson-dataformat-smile {:vsn "2.9.6", :native-prefix nil}, com.fasterxml.jackson.datatype/jackson-datatype-jsr310 {:vsn "2.12.0", :native-prefix nil}, javax.xml.bind/jaxb-api {:vsn "2.3.0", :native-prefix nil}, metosin/sieppari {:vsn "0.0.0-alpha13", :native-prefix nil}, aleph {:vsn "0.4.6", :native-prefix nil}, com.google.code.gson/gson {:vsn "2.7", :native-prefix nil}, metosin/reitit-interceptors {:vsn "0.5.18", :native-prefix nil}, net.jodah/expiringmap {:vsn "0.5.9", :native-prefix nil}, org.eclipse.jetty/jetty-alpn-server {:vsn "10.0.7", :native-prefix nil}}, :native-path "target/native"} {:native-path "target/native", :dependencies {com.cognitect/transit-java {:vsn "0.8.337", :native-prefix nil, :native? false}, metosin/reitit-middleware {:vsn "0.5.18", :native-prefix nil, :native? false}, selmer {:vsn "1.12.55", :native-prefix nil, :native? false}, args4j {:vsn "2.0.26", :native-prefix nil, :native? false}, com.github.houbb/pinyin {:vsn "0.3.1", :native-prefix nil, :native? false}, org.bouncycastle/bcprov-jdk15on {:vsn "1.62", :native-prefix nil, :native? false}, buddy/buddy-core {:vsn "1.6.0", :native-prefix nil, :native? false}, org.clojure/data.json {:vsn "0.2.6", :native-prefix nil, :native? false}, luminus-jetty {:vsn "0.2.3", :native-prefix nil, :native? false}, org.lz4/lz4-java {:vsn "1.8.0", :native-prefix nil, :native? false}, org.clojure/clojure {:vsn "1.12.0", :native-prefix nil, :native? false}, lambdaisland/deep-diff2 {:vsn "2.10.211", :native-prefix nil, :native? false}, primitive-math {:vsn "0.1.6", :native-prefix nil, :native? false}, io.github.classgraph/classgraph {:vsn "4.8.139", :native-prefix nil, :native? false}, com.googlecode.log4jdbc/log4jdbc {:vsn "1.2", :native-prefix nil, :native? false}, mount {:vsn "0.1.17", :native-prefix nil, :native? false}, org.clojure/tools.analyzer {:vsn "1.1.0", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-xml {:vsn "10.0.7", :native-prefix nil, :native? false}, com.zaxxer/HikariCP {:vsn "4.0.3", :native-prefix nil, :native? false}, clojure.java-time {:vsn "1.1.0", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-servlet {:vsn "10.0.7", :native-prefix nil, :native? false}, org.apache.poi/poi {:vsn "4.1.2", :native-prefix nil, :native? false}, org.apache.commons/commons-compress {:vsn "1.19", :native-prefix nil, :native? false}, ring/ring-devel {:vsn "1.9.6", :native-prefix nil, :native? false}, com.google.errorprone/error_prone_annotations {:vsn "2.0.18", :native-prefix nil, :native? false}, io.prometheus/simpleclient {:vsn "0.12.0", :native-prefix nil, :native? false}, org.apache.commons/commons-lang3 {:vsn "3.11", :native-prefix nil, :native? false}, org.clojure/tools.logging {:vsn "1.2.4", :native-prefix nil, :native? false}, org.nrepl/incomplete {:vsn "0.1.0", :native-prefix nil, :native? false}, metosin/ring-http-response {:vsn "0.9.3", :native-prefix nil, :native? false}, org.clojure/core.specs.alpha {:vsn "0.4.74", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-jetty-api {:vsn "10.0.7", :native-prefix nil, :native? false}, org.tukaani/xz {:vsn "1.9", :native-prefix nil, :native? false}, conman {:vsn "0.9.5", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-jetty-common {:vsn "10.0.7", :native-prefix nil, :native? false}, ns-tracker {:vsn "0.4.0", :native-prefix nil, :native? false}, hikari-cp {:vsn "2.14.0", :native-prefix nil, :native? false}, org.bouncycastle/bcpkix-jdk15on {:vsn "1.62", :native-prefix nil, :native? false}, metosin/muuntaja {:vsn "0.6.8", :native-prefix nil, :native? false}, io.netty/netty-common {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, lambdaisland/kaocha {:vsn "1.87.1366", :native-prefix nil, :native? false}, org.apiguardian/apiguardian-api {:vsn "1.0.0", :native-prefix nil, :native? false}, org.zeromq/jeromq {:vsn "0.4.3", :native-prefix nil, :native? false}, metosin/ring-swagger-ui {:vsn "4.3.0", :native-prefix nil, :native? false}, org.apache.poi/poi-ooxml-schemas {:vsn "4.1.2", :native-prefix nil, :native? false}, com.fasterxml.jackson.core/jackson-databind {:vsn "********", :native-prefix nil, :native? false}, byte-streams {:vsn "0.2.4", :native-prefix nil, :native? false}, org.clojure/spec.alpha {:vsn "0.5.238", :native-prefix nil, :native? false}, org.clojure/tools.cli {:vsn "1.0.214", :native-prefix nil, :native? false}, clj-stacktrace {:vsn "0.2.8", :native-prefix nil, :native? false}, org.eclipse.jetty.http2/http2-server {:vsn "10.0.7", :native-prefix nil, :native? false}, alkie/datascope {:vsn "0.1.2", :native-prefix nil, :native? false}, clj-time {:vsn "0.14.3", :native-prefix nil, :native? false}, mvxcvi/puget {:vsn "1.1.2", :native-prefix nil, :native? false}, com.github.houbb/nlp-common {:vsn "0.0.3", :native-prefix nil, :native? false}, expiring-map {:vsn "0.1.9", :native-prefix nil, :native? false}, ring/ring-ssl {:vsn "0.3.0", :native-prefix nil, :native? false}, crypto-random {:vsn "1.2.0", :native-prefix nil, :native? false}, org.codehaus.mojo/animal-sniffer-annotations {:vsn "1.14", :native-prefix nil, :native? false}, lambdaisland/clj-diff {:vsn "1.4.78", :native-prefix nil, :native? false}, jakarta.transaction/jakarta.transaction-api {:vsn "1.3.3", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-http {:vsn "10.0.7", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-util {:vsn "10.0.7", :native-prefix nil, :native? false}, com.taoensso/encore {:vsn "3.31.0", :native-prefix nil, :native? false}, metosin/spec-tools {:vsn "0.10.5", :native-prefix nil, :native? false}, compojure {:vsn "1.6.2", :native-prefix nil, :native? false}, org.clojure/tools.analyzer.jvm {:vsn "1.2.2", :native-prefix nil, :native? false}, io.netty/netty-handler-proxy {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, jarohen/chime {:vsn "0.3.4-20210630.135849-1", :native-prefix nil, :native? false}, riddley {:vsn "0.1.12", :native-prefix nil, :native? false}, com.github.houbb/heaven {:vsn "0.1.154", :native-prefix nil, :native? false}, net.incongru.watchservice/barbary-watchservice {:vsn "1.0", :native-prefix nil, :native? false}, commons-fileupload {:vsn "1.3.3", :native-prefix nil, :native? false}, camel-snake-kebab {:vsn "0.4.2", :native-prefix nil, :native? false}, lambdaisland/kaocha-cloverage {:vsn "1.1.89", :native-prefix nil, :native? false}, io.prometheus/simpleclient_tracer_otel {:vsn "0.12.0", :native-prefix nil, :native? false}, com.layerware/hugsql-adapter {:vsn "0.5.3", :native-prefix nil, :native? false}, io.netty/netty-codec {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, prone {:vsn "2021-04-23", :native-prefix nil, :native? false}, walmartlabs/system-viz {:vsn "0.4.0", :native-prefix nil, :native? false}, org.apache.commons/commons-math3 {:vsn "3.6.1", :native-prefix nil, :native? false}, org.clojure/tools.macro {:vsn "0.1.5", :native-prefix nil, :native? false}, org.zeromq/jnacl {:vsn "0.1.0", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-annotations {:vsn "10.0.7", :native-prefix nil, :native? false}, lambdaisland/deep-diff {:vsn "0.0-47", :native-prefix nil, :native? false}, realize {:vsn "2019-04-24", :native-prefix nil, :native? false}, com.fasterxml.jackson.dataformat/jackson-dataformat-cbor {:vsn "2.9.6", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-core-common {:vsn "10.0.7", :native-prefix nil, :native? false}, com.layerware/hugsql-core {:vsn "0.5.3", :native-prefix nil, :native? false}, com.googlecode.json-simple/json-simple {:vsn "1.1.1", :native-prefix nil, :native? false}, org.iq80.snappy/snappy {:vsn "0.4", :native-prefix nil, :native? false}, ring/ring-defaults {:vsn "0.3.2", :native-prefix nil, :native? false}, com.cognitect/transit-cljs {:vsn "0.8.256", :native-prefix nil, :native? false}, org.clojure/google-closure-library {:vsn "0.0-20170809-b9c14c6b", :native-prefix nil, :native? false}, io.aviso/pretty {:vsn "1.3", :native-prefix nil, :native? false}, cprop {:vsn "0.1.19", :native-prefix nil, :native? false}, fipp {:vsn "0.6.25", :native-prefix nil, :native? false}, io.netty/netty-codec-socks {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, luminus-migrations {:vsn "0.7.5", :native-prefix nil, :native? false}, ch.qos.logback/logback-core {:vsn "1.2.3", :native-prefix nil, :native? false}, org.clojure/clojurescript {:vsn "1.10.520", :native-prefix nil, :native? false}, io.netty/netty-buffer {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, aero {:vsn "1.1.6", :native-prefix nil, :native? false}, io.prometheus/simpleclient_tracer_common {:vsn "0.12.0", :native-prefix nil, :native? false}, com.stuartsierra/component {:vsn "0.3.2", :native-prefix nil, :native? false}, io.netty/netty-handler {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, org.flatland/ordered {:vsn "1.5.9", :native-prefix nil, :native? false}, luminus/ring-ttl-session {:vsn "0.3.3", :native-prefix nil, :native? false}, medley {:vsn "1.3.0", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-core-server {:vsn "10.0.7", :native-prefix nil, :native? false}, org.clojure/tools.namespace {:vsn "1.3.0", :native-prefix nil, :native? false}, org.webjars/webjars-locator-core {:vsn "0.50", :native-prefix nil, :native? false}, com.google.jsinterop/jsinterop-annotations {:vsn "1.0.0", :native-prefix nil, :native? false}, rhizome {:vsn "0.2.9", :native-prefix nil, :native? false}, org.eclipse.jetty.http2/http2-hpack {:vsn "10.0.7", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-plus {:vsn "10.0.7", :native-prefix nil, :native? false}, luminus-transit {:vsn "0.1.5", :native-prefix nil, :native? false}, com.nextjournal/beholder {:vsn "1.0.2", :native-prefix nil, :native? false}, metosin/reitit-swagger {:vsn "0.5.18", :native-prefix nil, :native? false}, com.github.spullara.mustache.java/compiler {:vsn "0.9.10", :native-prefix nil, :native? false}, eidolon {:vsn "0.2.0", :native-prefix nil, :native? false}, com.fasterxml.jackson.core/jackson-core {:vsn "2.13.2", :native-prefix nil, :native? false}, slingshot {:vsn "0.12.2", :native-prefix nil, :native? false}, org.yaml/snakeyaml {:vsn "1.31", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-servlet {:vsn "10.0.7", :native-prefix nil, :native? false}, org.tobereplaced/lettercase {:vsn "1.0.0", :native-prefix nil, :native? false}, org.ow2.asm/asm {:vsn "9.2", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-jetty-server {:vsn "10.0.7", :native-prefix nil, :native? false}, metosin/reitit-ring {:vsn "0.5.18", :native-prefix nil, :native? false}, cheshire {:vsn "5.8.1", :native-prefix nil, :native? false}, org.apache.poi/poi-scratchpad {:vsn "4.1.2", :native-prefix nil, :native? false}, lambdaisland/tools.namespace {:vsn "0.3.256", :native-prefix nil, :native? false}, meta-merge {:vsn "1.0.0", :native-prefix nil, :native? false}, org.apache.httpcomponents/httpcore {:vsn "4.4.5", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-security {:vsn "10.0.7", :native-prefix nil, :native? false}, markdown-clj {:vsn "1.11.3", :native-prefix nil, :native? false}, mvxcvi/arrangement {:vsn "2.1.0", :native-prefix nil, :native? false}, potemkin {:vsn "0.4.5", :native-prefix nil, :native? false}, jakarta.annotation/jakarta.annotation-api {:vsn "1.3.5", :native-prefix nil, :native? false}, clj-wallhack {:vsn "1.0.1", :native-prefix nil, :native? false}, hawk {:vsn "0.2.11", :native-prefix nil, :native? false}, io.djy/ezzmq {:vsn "0.8.2", :native-prefix nil, :native? false}, lambdaisland/kaocha-junit-xml {:vsn "1.17.101", :native-prefix nil, :native? false}, com.cognitect/transit-js {:vsn "0.8.846", :native-prefix nil, :native? false}, io.methvin/directory-watcher {:vsn "0.17.3", :native-prefix nil, :native? false}, progrock {:vsn "0.1.2", :native-prefix nil, :native? false}, metosin/reitit-core {:vsn "0.5.18", :native-prefix nil, :native? false}, org.mozilla/rhino {:vsn "1.7R5", :native-prefix nil, :native? false}, com.taoensso/truss {:vsn "1.6.0", :native-prefix nil, :native? false}, crypto-equality {:vsn "1.0.0", :native-prefix nil, :native? false}, funcool/cuerdas {:vsn "2.2.0", :native-prefix nil, :native? false}, ring/ring-headers {:vsn "0.3.0", :native-prefix nil, :native? false}, org.clojure/google-closure-library-third-party {:vsn "0.0-20170809-b9c14c6b", :native-prefix nil, :native? false}, com.fasterxml.jackson.core/jackson-annotations {:vsn "2.13.2", :native-prefix nil, :native? false}, pjstadig/humane-test-output {:vsn "0.11.0", :native-prefix nil, :native? false}, io.prometheus/simpleclient_hotspot {:vsn "0.12.0", :native-prefix nil, :native? false}, io.aleph/dirigiste {:vsn "0.1.6-alpha1", :native-prefix nil, :native? false}, org.apache.xmlbeans/xmlbeans {:vsn "3.1.0", :native-prefix nil, :native? false}, dk.ative/docjure {:vsn "1.14.0", :native-prefix nil, :native? false}, org.apache.poi/poi-ooxml {:vsn "4.1.2", :native-prefix nil, :native? false}, ring-cors {:vsn "0.1.13", :native-prefix nil, :native? false}, org.webjars/webjars-locator-jboss-vfs {:vsn "0.1.0", :native-prefix nil, :native? false}, com.google.javascript/closure-compiler-externs {:vsn "v20180805", :native-prefix nil, :native? false}, org.apache.commons/commons-text {:vsn "1.9", :native-prefix nil, :native? false}, org.javassist/javassist {:vsn "3.18.1-GA", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-webapp {:vsn "10.0.7", :native-prefix nil, :native? false}, org.clojure/java.classpath {:vsn "1.0.0", :native-prefix nil, :native? false}, io.prometheus/simpleclient_pushgateway {:vsn "0.12.0", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-alpn-java-server {:vsn "10.0.7", :native-prefix nil, :native? false}, commons-codec {:vsn "1.10", :native-prefix nil, :native? false}, com.google.guava/guava {:vsn "25.1-jre", :native-prefix nil, :native? false}, org.clojure/data.xml {:vsn "0.0.8", :native-prefix nil, :native? false}, json-html {:vsn "0.4.7", :native-prefix nil, :native? false}, cloverage {:vsn "1.2.4", :native-prefix nil, :native? false}, metosin/reitit-schema {:vsn "0.5.18", :native-prefix nil, :native? false}, borkdude/dynaload {:vsn "0.2.2", :native-prefix nil, :native? false}, adzerk/boot-test {:vsn "1.2.0", :native-prefix nil, :native? false}, org.webjars/webjars-locator {:vsn "0.45", :native-prefix nil, :native? false}, org.eclipse.jetty.http2/http2-common {:vsn "10.0.7", :native-prefix nil, :native? false}, org.msgpack/msgpack {:vsn "0.6.12", :native-prefix nil, :native? false}, commons-logging {:vsn "1.2", :native-prefix nil, :native? false}, borkdude/edamame {:vsn "0.0.19", :native-prefix nil, :native? false}, com.google.j2objc/j2objc-annotations {:vsn "1.1", :native-prefix nil, :native? false}, org.clojure/data.csv {:vsn "1.1.0", :native-prefix nil, :native? false}, com.taoensso/timbre {:vsn "6.0.1", :native-prefix nil, :native? false}, io.prometheus/simpleclient_tracer_otel_agent {:vsn "0.12.0", :native-prefix nil, :native? false}, metosin/malli {:vsn "0.8.2", :native-prefix nil, :native? false}, com.cognitect/transit-clj {:vsn "0.8.319", :native-prefix nil, :native? false}, com.github.seancorfield/next.jdbc {:vsn "1.2.796", :native-prefix nil, :native? false}, io.netty/netty-transport {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, com.github.oshi/oshi-core {:vsn "6.6.6", :native-prefix nil, :native? false}, org.ow2.asm/asm-commons {:vsn "9.2", :native-prefix nil, :native? false}, hiccup {:vsn "1.0.5", :native-prefix nil, :native? false}, io.netty/netty-transport-native-unix-common {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, digest {:vsn "1.4.7", :native-prefix nil, :native? false}, io.netty/netty-codec-dns {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, clj-commons/clj-yaml {:vsn "0.7.109", :native-prefix nil, :native? false}, io.netty/netty-codec-http {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, migratus {:vsn "1.4.2", :native-prefix nil, :native? false}, ring/ring-codec {:vsn "1.1.1", :native-prefix nil, :native? false}, metosin/reitit-malli {:vsn "0.5.18", :native-prefix nil, :native? false}, org.apache.httpcomponents/httpclient {:vsn "4.5.2", :native-prefix nil, :native? false}, ring-webjars {:vsn "0.2.0", :native-prefix nil, :native? false}, metosin/schema-tools {:vsn "0.12.3", :native-prefix nil, :native? false}, org.clojure/core.rrb-vector {:vsn "0.1.2", :native-prefix nil, :native? false}, ring/ring-anti-forgery {:vsn "1.3.0", :native-prefix nil, :native? false}, prismatic/schema {:vsn "1.1.12", :native-prefix nil, :native? false}, org.webjars.npm/material-icons {:vsn "1.10.8", :native-prefix nil, :native? false}, com.bhauman/spell-spec {:vsn "0.1.2", :native-prefix nil, :native? false}, manifold {:vsn "0.1.9-alpha4", :native-prefix nil, :native? false}, com.layerware/hugsql-adapter-next-jdbc {:vsn "0.5.3", :native-prefix nil, :native? false}, instaparse {:vsn "1.4.8", :native-prefix nil, :native? false}, org.checkerframework/checker-qual {:vsn "2.0.0", :native-prefix nil, :native? false}, clout {:vsn "2.2.1", :native-prefix nil, :native? false}, net.java.dev.jna/jna {:vsn "5.16.0", :native-prefix nil, :native? false}, org.apache.poi/ooxml-schemas {:vsn "1.4", :native-prefix nil, :native? false}, metosin/reitit-sieppari {:vsn "0.5.18", :native-prefix nil, :native? false}, com.zaxxer/SparseBitSet {:vsn "1.2", :native-prefix nil, :native? false}, com.andrewmcveigh/cljs-time {:vsn "0.5.2", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-jndi {:vsn "10.0.7", :native-prefix nil, :native? false}, org.clojure/core.match {:vsn "1.0.0", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-io {:vsn "10.0.7", :native-prefix nil, :native? false}, org.clojure/tools.reader {:vsn "1.3.6", :native-prefix nil, :native? false}, io.prometheus/simpleclient_common {:vsn "0.12.0", :native-prefix nil, :native? false}, joda-time {:vsn "2.9.9", :native-prefix nil, :native? false}, org.tcrawley/dynapath {:vsn "1.1.0", :native-prefix nil, :native? false}, nrepl {:vsn "1.0.0", :native-prefix nil, :native? false}, tigris {:vsn "0.1.1", :native-prefix nil, :native? false}, clj-commons/iapetos {:vsn "0.1.14", :native-prefix nil, :native? false}, org.eclipse.jetty.toolchain/jetty-servlet-api {:vsn "4.0.6", :native-prefix nil, :native? false}, net.java.dev.jna/jna-platform {:vsn "5.16.0", :native-prefix nil, :native? false}, com.taoensso/nippy {:vsn "3.2.0", :native-prefix nil, :native? false}, clj-http {:vsn "2.3.0", :native-prefix nil, :native? false}, javax.servlet/javax.servlet-api {:vsn "3.1.0", :native-prefix nil, :native? false}, net.clojars.zhaoyul/monitor-lib {:vsn "0.1.2-20250729.055020-4", :native-prefix nil, :native? false}, metosin/reitit-http {:vsn "0.5.18", :native-prefix nil, :native? false}, info.sunng/ring-jetty9-adapter {:vsn "0.17.2", :native-prefix nil, :native? false}, buddy/buddy-auth {:vsn "2.2.0", :native-prefix nil, :native? false}, org.apache.commons/commons-collections4 {:vsn "4.4", :native-prefix nil, :native? false}, io.netty/netty-resolver {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, com.rpl/specter {:vsn "1.1.4", :native-prefix nil, :native? false}, io.netty/netty-transport-native-epoll {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, org.xerial/sqlite-jdbc {:vsn "********", :native-prefix nil, :native? false}, org.webjars.npm/bulma {:vsn "0.9.4", :native-prefix nil, :native? false}, org.slf4j/slf4j-api {:vsn "1.7.25", :native-prefix nil, :native? false}, walmartlabs/datascope {:vsn "0.1.1", :native-prefix nil, :native? false}, expound {:vsn "0.9.0", :native-prefix nil, :native? false}, buddy/buddy-sign {:vsn "3.1.0", :native-prefix nil, :native? false}, com.google.javascript/closure-compiler-unshaded {:vsn "v20180805", :native-prefix nil, :native? false}, org.clojure/test.check {:vsn "1.1.1", :native-prefix nil, :native? false}, metosin/reitit-frontend {:vsn "0.5.18", :native-prefix nil, :native? false}, io.netty/netty-resolver-dns {:vsn "4.1.25.Final", :native-prefix nil, :native? false}, org.ow2.asm/asm-analysis {:vsn "9.2", :native-prefix nil, :native? false}, funcool/struct {:vsn "1.4.0", :native-prefix nil, :native? false}, com.github.virtuald/curvesapi {:vsn "1.06", :native-prefix nil, :native? false}, metosin/reitit-spec {:vsn "0.5.18", :native-prefix nil, :native? false}, clj-tuple {:vsn "0.2.2", :native-prefix nil, :native? false}, metosin/jsonista {:vsn "0.3.1", :native-prefix nil, :native? false}, com.google.protobuf/protobuf-java {:vsn "3.0.2", :native-prefix nil, :native? false}, com.carouselapps/to-jdbc-uri {:vsn "0.5.0", :native-prefix nil, :native? false}, metosin/reitit-swagger-ui {:vsn "0.5.18", :native-prefix nil, :native? false}, metosin/reitit {:vsn "0.5.18", :native-prefix nil, :native? false}, babashka/fs {:vsn "0.5.24", :native-prefix nil, :native? false}, ring/ring-servlet {:vsn "1.9.4", :native-prefix nil, :native? false}, ch.qos.logback/logback-classic {:vsn "1.2.3", :native-prefix nil, :native? false}, org.clojure/core.memoize {:vsn "1.0.253", :native-prefix nil, :native? false}, com.stuartsierra/dependency {:vsn "0.2.0", :native-prefix nil, :native? false}, tech.droit/clj-diff {:vsn "1.0.1", :native-prefix nil, :native? false}, net.i2p.crypto/eddsa {:vsn "0.3.0", :native-prefix nil, :native? false}, dorothy {:vsn "0.0.6", :native-prefix nil, :native? false}, org.clojure/data.priority-map {:vsn "1.1.0", :native-prefix nil, :native? false}, org.clojure/java.data {:vsn "1.0.95", :native-prefix nil, :native? false}, ring/ring-mock {:vsn "0.4.0", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-server {:vsn "10.0.7", :native-prefix nil, :native? false}, commons-io {:vsn "2.6", :native-prefix nil, :native? false}, io.netty/netty-all {:vsn "4.1.68.Final", :native-prefix nil, :native? false}, org.apache.httpcomponents/httpmime {:vsn "4.5.2", :native-prefix nil, :native? false}, com.google.code.findbugs/jsr305 {:vsn "3.0.1", :native-prefix nil, :native? false}, metosin/reitit-dev {:vsn "0.5.18", :native-prefix nil, :native? false}, ring/ring-core {:vsn "1.7.1", :native-prefix nil, :native? false}, org.clojure/core.cache {:vsn "1.0.225", :native-prefix nil, :native? false}, org.ow2.asm/asm-tree {:vsn "9.2", :native-prefix nil, :native? false}, org.clojure/core.async {:vsn "1.6.673", :native-prefix nil, :native? false}, com.fasterxml.jackson.dataformat/jackson-dataformat-smile {:vsn "2.9.6", :native-prefix nil, :native? false}, com.fasterxml.jackson.datatype/jackson-datatype-jsr310 {:vsn "2.12.0", :native-prefix nil, :native? false}, javax.xml.bind/jaxb-api {:vsn "2.3.0", :native-prefix nil, :native? false}, metosin/sieppari {:vsn "0.0.0-alpha13", :native-prefix nil, :native? false}, aleph {:vsn "0.4.6", :native-prefix nil, :native? false}, com.google.code.gson/gson {:vsn "2.7", :native-prefix nil, :native? false}, metosin/reitit-interceptors {:vsn "0.5.18", :native-prefix nil, :native? false}, net.jodah/expiringmap {:vsn "0.5.9", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-alpn-server {:vsn "10.0.7", :native-prefix nil, :native? false}}}]