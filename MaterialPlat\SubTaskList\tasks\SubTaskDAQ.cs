using System.Text.Json;
using static Logging.CCSSLogger;
using Consts;
using static Scripting.ITemplate;
using SubTaskUtils;
using SignalExample;
using MQ;
using Scripting;
using System.Reactive.Subjects;
using ScriptEngine.DBHandler.TableWriters;
using ScriptEngine.InputVar.InputVars;

namespace SubTasks;

/// <summary>
/// Class <c>SubTaskDAQ</c>
///
/// DAQ子任务:
///     * 参数:
///         control_input_bufferCode : 绑定Buffer输入变量code
///     * 实现:
///         TODO:
///     * 监听:
///         多任务管理器专用命令
///         多任务管理器通用命令
///         硬件数据
///        TODO: ??
///         UI消息
///         脚本执行器消息
///
///
/// </summary>
public class SubTaskDAQ : ISubTask
{
    public MQSubTaskSub[] subs { set; get; }
    public bool Sub_TASK_MGR_CMD { get; set; } = true;
    public bool Sub_TASK_MGR_CMD_Other { get; set; } = true;
    public bool Sub_TASK_HARDWARE_CMD { get; set; } = false;
    public bool Sub_TASK_HARDWARE_DATA { get; set; } = true;
    public bool Sub_TOPIC_FROM_UI { get; set; } = true;
    public bool Sub_TOPIC_FROM_SCRIPT_CLIENT { get; set; } = true;
    public bool Sub_SelfTopic { get; set; } = false;
    public bool Sub_TOPIC_NOTIFY { get; set; } = false;

    private string? _processID;
    private string? _subtaskID;
    private string? _templateName;
    private string? _sample_Instance_Code;
    private string? bindBuffercode;
    //是否清零时间戳
    private bool _timeResetZero = false;
    private string _signal_timeCode = "";
    // 前端传参中，默认是false=>默认是终止脚本中的数据流
    private bool _keepStream;
    private double _moveInterval = 0;
    private double _timeInterval = 0;
    private double _loadInterval = 0;
    private double _strainInterval = 0;
    private ISubject<MsgToDB> _saveDb = new Subject<MsgToDB>();
    private double _moveLast = double.NaN;
    private double _timeLast = double.NaN;
    private double _loadLast = double.NaN;
    private DynamicDataWriter? writer;
    private ITemplate? templateInst;

    // 不同daq如果有相同的key,这意味着使用同一个Observable 对数据进行处理。例如推送前端或者存库
    private string? _key;

    public record DAQVariable(string Code, string Name, double Value, int Index);

    public class UICmdParam
    {
        public List<DAQVariable>? VarValues { get; set; }
    }

    public SubTaskCmdParams ImportParams(string ParamatersString)
    {
        Logger.Info("ImportPrams" + " :" + ParamatersString);

        var x = JsonSerializer.Deserialize<SubTaskCmdParams>(ParamatersString);
        Logger.Info("Json 反序列化" + x);
        return x!;
    }

    public string GenrateStatusUIJson(string status_str)
    {
        var uicmdParam = new UIStatusParam(status_str);
        // 构建 JSON 字符串
        var jsonStr = JsonSerializer.Serialize(uicmdParam);

        // 通过解析 JSON 字符串创建 JsonDocument 对象
        using JsonDocument document = JsonDocument.Parse(jsonStr);
        // 获取根节点的 JsonElement
        JsonElement root = document.RootElement;

        // 构建 UICmdParams 对象，并将 UIParams 设置为 JsonElement
        var uicmdParams = new UICmdParams(_processID!, _subtaskID!, "taskStatus", root);

        return JsonSerializer.Serialize(uicmdParams);
    }

    /// 在创建时返回一个 bool值, 代表是否创建成功
    public bool Run(SubTaskCmdParams t)
    {
        // 模板信息
        _processID = t!.ProcessID!;
        _subtaskID = t!.SubTaskID!;
        //_sample_Instance_Code = t.SampleName;
        _templateName = t.ClassName!;
       
        Logger.Info(t.SubTaskParams);

        // TODO: 子任务父类中统一实现
        // 子任务状态同步
        ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(t.Cmd()!));
        Logger.Info("DAQ子任务 启动:" + t);

        // **参数解析**
        templateInst = GetTemplateByName(t.ClassName!);

        _sample_Instance_Code = templateInst.CurrentInst.Code;

        // 绑定Buffer输入变量
        bindBuffercode = UtilsForSubTasks.ReadVarValue<string>(templateInst,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_bufferCode"));
        BufferInputVar daqBuffer = templateInst.GetVarByName<BufferInputVar>(bindBuffercode);

        // 是否清空buffer
        string bufferMode = UtilsForSubTasks.ReadVarValue<string>(templateInst,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_bufferMode"));
        if (bufferMode == "RESTART")
        {
            // 清空缓冲区时, 重置数据表, 通知前端重新开始
            daqBuffer.Reset();
            templateInst!.Db!.RestartBuffer(_sample_Instance_Code!, bindBuffercode!);
            ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(new UICmdParams(
                _processID, 
                _subtaskID, 
                "BufferReset", 
                JsonDocument.Parse(JsonSerializer.Serialize(new
                {
                    SampleInstCode = _sample_Instance_Code,
                    BufferCode = bindBuffercode,
                    BufferMode = bufferMode
                })).RootElement)));
        }
        //是否清零时间通道
        _timeResetZero = UtilsForSubTasks.ReadInputVarProperty<bool>(_templateName,
                   t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_timeResetZero"), "IsCheck");
        if (_timeResetZero)
        {
            _signal_timeCode = UtilsForSubTasks.ReadVarValue<string>(_templateName,
                  t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_timeResetZero"));

        }
        // 是否持久化标识
        bool saveDB = (bool)UtilsForSubTasks.ReadVarValue<bool>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_saveDB"));
        // 存buffer(buffer长度可以设置为0以节省内存)
        const bool saveBuffer = true;
        // 推送ui
        const bool sendToUi = true;

        // 结束时停止数据流
        _keepStream = (bool)UtilsForSubTasks.ReadVarValue<bool>(_templateName,
           t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_keepStream"));
        // 读取时间、位移、负荷间隔的值、是否被选定、Mode
        _moveInterval = UtilsForSubTasks.ReadVarValue<double>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_move_interval"));
        _timeInterval = UtilsForSubTasks.ReadVarValue<double>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_time_interval"));
        _loadInterval = UtilsForSubTasks.ReadVarValue<double>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_load_interval"));
        _strainInterval = UtilsForSubTasks.ReadVarValue<double>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_strain_interval"));
        // 控制模式
        string moveMode = UtilsForSubTasks.ReadInputVarProperty<string>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_move_interval"), "Mode");
        string timeMode = UtilsForSubTasks.ReadInputVarProperty<string>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_time_interval"), "Mode");
        string loadMode = UtilsForSubTasks.ReadInputVarProperty<string>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_load_interval"), "Mode");
        string strainMode = UtilsForSubTasks.ReadInputVarProperty<string>(_templateName,
          t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_strain_interval"), "Mode");
        // IsCheck
        bool moveIsCheck = UtilsForSubTasks.ReadInputVarProperty<bool>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_move_interval"), "IsCheck");
        bool timeIsCheck = UtilsForSubTasks.ReadInputVarProperty<bool>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_time_interval"), "IsCheck");
        bool loadIsCheck = UtilsForSubTasks.ReadInputVarProperty<bool>(_templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_load_interval"), "IsCheck");
        bool strainIsCheck = UtilsForSubTasks.ReadInputVarProperty<bool>(_templateName,
           t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_strain_interval"), "IsCheck");
       

        _key = bindBuffercode;

        var daqHandlerParam = new DaqHandlerParameters(templateInst, _timeInterval, daqBuffer,
           timeIsCheck, _subtaskID, _sample_Instance_Code,
           sendToUi, saveBuffer, saveDB,
           timeMode, moveIsCheck, _moveInterval, moveMode,
           loadIsCheck, _loadInterval, loadMode,
           strainIsCheck,_strainInterval,strainMode,_timeResetZero,_signal_timeCode
           );

        templateInst.CreateOrUpdateDaqHandler(_key, _processID, daqHandlerParam);

        return true;
    }


    // 终止执行
    public bool Abort(SubTaskCmdParams Params)
    {
        ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD));
        Logger.Info("DAQ 终止:" + Params);
        ((ISubTask)this).CleanAllSubs();
        // 结束后台数据流
        if (!_keepStream && templateInst != null && _key != null && _processID != null)
        {
            templateInst.RemoveDaqHandler(_key + _processID);
        }
        else
        {
            if (!_keepStream)
            {
             Logger.Error($"终止时未找到对应的后台数据流_key={_key},_processID={_processID}，无法停止后台数据流！_templateName={_templateName}");
            }
        }
            
            
        return true;
    }

    // 暂停执行
    public bool Pause(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(CmdConsts.RCV_PAUSE_TASK_CMD));
        return true;
    }

    // 恢复执行
    public bool Resume(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(CmdConsts.RCV_RESUME_TASK_CMD));
        return true;
    }

    // 结束执行
    public bool Finish(SubTaskCmdParams Params)
    {
        Logger.Info("DAQ 结束:" + Params);
        ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(CmdConsts.RCV_FINISH_TASK_CMD));
        ISystemBus.SendToTaskUpTopic(CmdConsts.SubTaskFinishCmd(Params.ClassName!, Params.ProcessID!, Params.SubTaskID!));
        ((ISubTask)this).CleanAllSubs();

        return true;
    }

    public bool ProcessData(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    public void ImportHwFuncRet(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromUI(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public string[] GetSelfTopic()
    {
        string[] self_topics = Array.Empty<string>();
        return self_topics;
    }

    public void HandleMsgFromVAR(string topic, string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromScript(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    private static double? GetElementAtIndex(double[]? array, int index)
    {
        if (array == null)
        {
            return null;
        }
        else if (index >= 0 && index < array.Length)
        {
            return array[index];
        }
        else
        {
            return null;
        }
    }

    public bool ReStart(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    public JsonElement UIParams()
    {
        throw new NotImplementedException();
    }

    public bool Error(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    public void HandleNotify(string notifyTitle, string msg)
    {
        throw new NotImplementedException();
    }
}