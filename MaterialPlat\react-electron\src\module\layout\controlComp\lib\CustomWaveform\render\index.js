import React, { useEffect, useMemo, useState } from 'react'
import { Card, message } from 'antd'
import cloneDeep from 'lodash/cloneDeep'

import { initalWaveParams } from '@/components/formItems/customWaveformParamsItem/constants'
import { updateInputVar } from '@/utils/services'
import useInputVariableByCode from '@/hooks/project/inputVariable/useInputVariableByCode'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

import { CustomWaveformContanier } from './style'
import SingleWaveformCard, { EmptyWaveFormCard } from './singleWaveformCard'

const Render = ({
    config: {
        variable: {
            value: valv
        } = {}
    }
}) => {
    const valueVari = useInputVariableByCode(valv?.code)

    const [value, setValue] = useState([])

    // 波形的配置
    const cacheWaveformConfig = useMemo(() => {
        // 判断输入变量中有没有配置的数据（主要选择了量纲）
        if (!valueVari?.custom_array_tab?.customWaveform) {
            return cloneDeep(initalWaveParams)
        }
        return valueVari?.custom_array_tab?.customWaveform
    }, [valueVari?.custom_array_tab])

    useEffect(() => {
        if (valueVari?.default_val?.value) {
            setValue(valueVari?.default_val?.value)
        }
    }, [valueVari?.default_val?.value])

    // 新增波段
    const handleAddWaveform = () => {
        if (!valueVari) {
            message.error('没有绑定变量')
            return
        }
        // TODO 是否要添加默认值
        setValue([...value, {}])
    }

    // 删除波段
    const handleDelWaveform = (index) => {
        const newValue = value.filter((_, i) => i !== index)
        setValue(newValue)
        handleSaveChange(newValue)
    }

    // 更新单个波形参数
    const handleValueChange = (val, index) => {
        const newValue = value.map((item, i) => (i !== index ? item : { ...val }))
        setValue(newValue)
        handleSaveChange(newValue)
    }

    // 保存
    const handleSaveChange = (newValue) => {
        onChange({
            ...valueVari,
            default_val: {
                ...valueVari?.default_val,
                value: newValue
            }
        })
    }

    const onChange = async (v) => {
        const res = await updateInputVar(v)

        if (res) {
            dispatchSyncInputVar({ code: v.code }, v)
        }
    }

    return (
        <CustomWaveformContanier>
            {
                value.map((data, index) => (
                    <SingleWaveformCard
                        key={index + 1}
                        order={index + 1}
                        data={data}
                        renderConfig={cacheWaveformConfig}
                        onCheckedChange={() => handleDelWaveform(index)}
                        onValueChange={(newValue) => handleValueChange(newValue, index)}
                    />
                ))
            }
            <EmptyWaveFormCard order={value.length + 1} onCheckedChange={handleAddWaveform} />
        </CustomWaveformContanier>
    )
}

export default Render
