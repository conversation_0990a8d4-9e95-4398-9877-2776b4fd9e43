import React, { useEffect, useState } from 'react'
import { message } from 'antd'
import debounce from 'lodash/debounce'

import { updateInputVar } from '@/utils/services'
import useInputVariableByCode from '@/hooks/project/inputVariable/useInputVariableByCode'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

import { CustomWaveformContanier } from './style'
import CardItem, { EmptyCardItem } from './CardItem'

const Render = ({
    config: {
        variable: {
            value: valv
        } = {}
    }
}) => {
    const valueVari = useInputVariableByCode(valv?.code)
    const programmableParametersData = valueVari?.custom_array_tab?.programmableParameters || []

    const [value, setValue] = useState([])

    useEffect(() => {
        if (valueVari?.default_val?.value) {
            setValue(valueVari?.default_val?.value)
        }
    }, [valueVari?.default_val?.value])

    // 新增
    const handleAddForm = () => {
        if (!valueVari) {
            message.error('没有绑定变量')
            return
        }
        // TODO 是否要添加默认值
        setValue([...value, {}])
    }

    // 删除
    const handleDelForm = (index) => {
        const newValue = value.filter((_, i) => i !== index)
        setValue(newValue)
        handleSaveChange(newValue)
    }

    // 更新
    const onChangeHandle = debounce((val, index) => {
        const newValue = value.map((item, i) => (i !== index ? item : { ...val }))
        setValue(newValue)
        handleSaveChange(newValue)
    }, 200)

    // 保存
    const handleSaveChange = (newValue) => {
        onChange({
            ...valueVari,
            default_val: {
                ...valueVari?.default_val,
                value: newValue
            }
        })
    }

    const onChange = async (v) => {
        const res = await updateInputVar(v)
        if (res) {
            dispatchSyncInputVar({ code: v.code }, v)
        }
    }

    return (
        <CustomWaveformContanier>
            {
                value.map((data, index) => (
                    <CardItem
                        data={data}
                        programmableParametersData={programmableParametersData}
                        onCheckedChange={() => handleDelForm(index)}
                        onChange={(val) => onChangeHandle(val, index)}
                        key={index + 1}
                        order={index + 1}
                    />
                ))
            }
            <EmptyCardItem order={value.length + 1} onCheckedChange={handleAddForm} />
        </CustomWaveformContanier>
    )
}

export default Render
