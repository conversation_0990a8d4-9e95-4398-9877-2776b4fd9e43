import { useDispatch } from 'react-redux'

import { getProjectId } from '@/utils/auth'
import { getProcessID } from '@/utils/utils'
import { PROJECT_RESULT_HISTORY_DATA } from '@/redux/constants/project'
import { getHistoryResult } from '@/utils/services'

const projectHistory = () => {
    const dispatch = useDispatch()

    const initProjectHistoryData = async () => {
        try {
            if (getProjectId()) {
                const res = await getHistoryResult({ TemplateName: getProcessID() })
                if (res) {
                    dispatch({
                        type: PROJECT_RESULT_HISTORY_DATA,
                        param: res
                    })
                }
            }
        } catch (error) {
            console.log(error)
        }
    }

    return {
        initProjectHistoryData
    }
}

export default projectHistory
