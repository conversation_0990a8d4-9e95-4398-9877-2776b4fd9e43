/* eslint-disable no-case-declarations */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
import React, {
    useEffect, useState, useRef, useMemo, useCallback
} from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { message, notification } from 'antd'
import { StaticChartLine } from '@/components/charts/index'
import { ChartLineOption, SELECT_POINT_TYPES } from '@/components/charts/StaticChartLine/constants'
import { auxiliaryDataType } from '@/pages/dialog/auxiliaryLineModal/components/saveModal/constants'
import useStaticCurve from '@/hooks/useStaticCurve'
import useSample from '@/hooks/useSample'
import { SETTING_FORMS, DISPLAY_TYPE, Y_TYPE } from '@/pages/dialog/staticCurveManage/contants'
import {
    CLEAR_BY_ID,
    CREATE_RES_DATA,
    UPDATE_CURRENT_POINT,
    ACCPET_BREAK_POINT,
    UPDATE_SET_MARKING_DATA,
    UPDATE_SET_BREAK_FLAG,
    UPDATE_SETTING_RES_MENU
} from '@/redux/constants/staticCurve'
import {
    getProcessID, numberFormat, resultFractionalDigit, unitConversion
} from '@/utils/utils'
import useLifecycleAPI, { DATA_SROUCE_TYPE_MAP } from '@/hooks/controlComp/useLifecycleAPI'

import { INPUT_VAIABLE_SELECT_OPTIONS_TYPE } from '@/utils/constants'
import { batchUpdateInputVar } from '@/utils/services'
import useResult from '@/hooks/useResult'
import useProjectHistory from '@/hooks/useProjectHistory'
import { setWidGetStatus, getWidGetStatus } from '@/utils/auth'
import { useTranslation } from 'react-i18next'
import useAction from '@/hooks/useAction'
import useNumberInputVariable from '@/hooks/project/inputVariable/useNumberInputVariable'
import useSelectInputVariable from '@/hooks/project/inputVariable/useSelectInputVariable'
import useSubScriberCompMsg from '@/hooks/subscribe/useSubScriberCompMsg'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

import moment from 'moment/moment'
import * as XLSX from 'xlsx'
import { convertOldConfigToNew } from '@/module/layout/controlComp/lib/CurveDaqBuffer/utils/convertOldConfigToNew'
import ContextMenuComponent from './components/contextMenu'
import CreateResModal from './components/createResModal'
import BlockTagDiv from './components/blockTagDiv'
import LegendDiv from './components/legendDiv'
import HandMarkingModal from './components/handMarkingModal'
import useCrossIndexSync from './hooks/useCrossIndexSync'

import { settingParamsToChartOpts } from './constants'
import { PageContainer, ChartContainer } from './style'

const exportMultipleSheets = (sheets, fileName) => {
    const workbook = XLSX.utils.book_new()
    sheets.forEach((sheet) => {
        const worksheet = XLSX.utils.aoa_to_sheet(sheet.data)
        XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name)
    })
    XLSX.writeFile(workbook, fileName)
}

const StaticCurve = (props) => {
    const {
        id: layoutUnitId = 'sunGongDaShuaiBi', // 布局的id, 绑定右击菜单, 唯一组件用
        item: widgetItem, // 布局元素信息
        isRightClick = true, // 是否右键
        layoutConfig,
        isFission
    } = props
    // 获取当前曲线设置id, 没有就完犊子了
    const currentSettingIdFromWidget = widgetItem?.widget_data_source ? JSON.parse(widgetItem?.widget_data_source) : null
    const layoutCurvetId = `${layoutUnitId}`
    const dispatch = useDispatch()
    const { t } = useTranslation()
    const [messageApi, contextHolder] = message.useMessage()
    const [nApi, nContextHolder] = notification.useNotification()

    const { initStaticCurveById, getAuxiliaryLines } = useStaticCurve()
    const inputVariableNumber = useNumberInputVariable()
    const inputVariableSelect = useSelectInputVariable()
    const inputVariableSelectCustom = useMemo(
        () => inputVariableSelect.filter(v => v.select_tab.selection === INPUT_VAIABLE_SELECT_OPTIONS_TYPE.列表中的单选项.value),
        [inputVariableSelect]
    )
    const { startAction } = useAction()
    const { handleSampleData, getSamples } = useSample()
    const { getCurveResults } = useResult()
    const { initProjectHistoryData } = useProjectHistory()
    const childrenState = useSelector(state => state.staticCurve.childrenState)

    const settingResList = useSelector(state => state.staticCurve.settingResList)
    const openExperiment = useSelector(state => state.subTask.openExperiment)
    const widgetData = useSelector(state => state.template.widgetData)
    const actionList = useSelector(state => state.template.actionList)
    const signalList = useSelector(state => state.template.signalList)
    const resultData = useSelector(state => state.template.resultData)
    const resultTestData = useSelector(state => state.template.resultTestData)
    const optSample = useSelector(state => state.project.optSample)
    const multiSample = useSelector(state => state.project.multiSample)
    const resultHistoryData = useSelector(state => state.project.resultHistoryData)

    const [option, setOption] = useState()
    const historyDataRef = useRef({})
    const [chartPointType, setChartPointType] = useState('')
    const [rests, setRests] = useState({}) // 曲线除了数据线其他的数据源
    const [tagValues, setTagValues] = useState()
    const [lineTagShow, setLineTagShow] = useState(false) // 折线上的tag显示开关
    const [blockTagShow, setBlockTagShow] = useState(false) // 标签块显示与否
    const chartRef = useRef()

    const currentState = useMemo(() => {
        const tempCurrent = (currentSettingIdFromWidget in childrenState) && childrenState[currentSettingIdFromWidget]
        if (tempCurrent?.settingModalData) {
            console.log('1111', convertOldConfigToNew(tempCurrent?.settingModalData))

            const {
                coordinate_source_flag,
                coordinate_source_input_code,
                coordinate_source_select,
                x_channel,
                y_channel,
                x_label,
                y_label,
                x_units,
                y_units
            } = tempCurrent?.settingModalData
            if (coordinate_source_flag) {
                const inputValue = inputVariableSelectCustom?.find(f => f.code === coordinate_source_input_code)?.default_val?.value
                const coordinate = coordinate_source_select?.find(f => f.value === inputValue)
                return {
                    ...tempCurrent,
                    settingModalData: {
                        ...tempCurrent?.settingModalData,
                        x_channel: coordinate?.x ?? x_channel,
                        x_label: coordinate?.x_label ?? x_label,
                        x_units: coordinate?.x_units ?? x_units,
                        y_label: coordinate?.y_label ?? y_label,
                        y_channel: Array.isArray(coordinate?.y ?? y_channel) ? coordinate?.y ?? y_channel : [coordinate?.y ?? y_channel],
                        y_units: coordinate?.y_units ?? y_units
                    }
                }
            }
            return tempCurrent
        }
        return tempCurrent
    }, [currentSettingIdFromWidget,
        childrenState,
        settingResList,
        inputVariableSelectCustom])

    const currentStateRef = useRef(currentState)

    currentStateRef.current = currentState
    // 使用十字线同步 hook
    const {
        autoIndex,
        callBackOriginIndex
    } = useCrossIndexSync({
        currentState,
        optSample,
        chartPointType
    })

    // 曲线上要显示的结果变量
    const resultsVarsList = useMemo(() => {
        return getCurveResults()
    }, [resultData,
        resultTestData])

    const dataCodes = useMemo(() => {
        if (!currentState?.settingModalData) {
            return []
        }
        const { x_channel, y_channel, y2_channel } = currentState?.settingModalData

        return Array.from(new Set([x_channel, ...(y_channel ?? []), ...(y2_channel ?? [])]))
    }, [currentState?.settingModalData])

    const { targetRef } = useLifecycleAPI({
        controlCompId: layoutUnitId,
        dataSourceType: option && DATA_SROUCE_TYPE_MAP.daqbuffer, // 等生成配置后再开始订阅数据防止数据上来比表格加载快
        dataSourceCode: currentState?.settingModalData?.buffer_code,
        dataCodes,
        timer: 500,
        number: -1, //
        testStatus: (currentState?.settingModalData?.[SETTING_FORMS.显示.key] === DISPLAY_TYPE.结果文件 || !openExperiment) ? 0 : 1,
        daqCurveSelectedSampleCodes: useMemo(() => {
            if (currentState?.settingModalData?.[SETTING_FORMS.显示.key] === DISPLAY_TYPE.结果文件) {
                if (multiSample?.length > 0) {
                    return multiSample?.map(f => f.code)
                }

                return getSamples(true).map(s => s.code)
            }

            return [optSample.code]
        }, [optSample, currentState?.settingModalData?.[SETTING_FORMS.显示.key]])
    })

    useSubScriberCompMsg({
        controlCompId: layoutUnitId,
        onMessage: (msg) => {
            const {
                mode, data, doubleArrayIndex, sampleCode
            } = msg
            // 动态获取所有字段的数组长度
            const fieldNames = Object.keys(data)
            const maxLength = Math.max(
                ...fieldNames.map(field => data[field]?.length || 0)
            )

            // 将数组数据转换为目标格式、

            const allData = []

            for (let i = 0; i < maxLength; i += 1) {
                const dataItem = []

                // 动态处理每个字段
                fieldNames.forEach(fieldName => {
                    dataItem.push({
                        Code: fieldName,
                        Name: fieldName,
                        Value: data[fieldName]?.[i],
                        Index: -1
                    })
                })

                allData.push(dataItem)
            }

            const sample = getSamples().find(f => f.code === sampleCode)

            if (mode === 0) {
                chartRef.current?.clearAllLine?.()
            }

            if (sample) {
                const handledData = handleSampleData({
                    sample,
                    taskData: allData,
                    x: currentState?.settingModalData?.x_channel,
                    y: currentState?.settingModalData?.y_channel,
                    y2: currentState?.settingModalData?.[SETTING_FORMS.曲线.key]?.length === 2 ? currentState?.settingModalData.y2_channel : null
                })

                // 再加新数据
                // 优化：直接调用chart组件的方法而不是更新状态
                if (chartRef.current) {
                    chartRef.current?.updateData?.(handledData)
                }
            }
        }
    })

    /**
     * 设置图表参数信息
     */
    useEffect(() => {
        if (currentState) {
            const {
                settingModalData, crossLineActive, setBreakFlag, markingData, createResData
            } = currentState
            if (!settingModalData) {
                console.log('没有曲线设置数据啊, 小伙儿还玩啥')
                return
            }
            const newOpt = settingParamsToChartOpts({
                params: settingModalData,
                labelToKey: Object.fromEntries(signalList.map(s => [s.code, s.code]))
            })

            setOption({ ...newOpt })
            setLineTagShow(settingModalData[SETTING_FORMS.显示线标注开关.key])
            setBlockTagShow(settingModalData[SETTING_FORMS.显示标签块开关.key])

            // 处理激活十字线
            if (setBreakFlag) {
                setChartPointType(SELECT_POINT_TYPES.break.key)
            } else if (crossLineActive) {
                setChartPointType(SELECT_POINT_TYPES.cross.key)
            } else if (markingData.handMarkingActive) {
                setChartPointType(SELECT_POINT_TYPES.marking.key)
            } else if (createResData.lineActive) {
                setChartPointType(SELECT_POINT_TYPES.createRes.key)
            } else {
                setChartPointType('')
            }
        } else {
            setOption(ChartLineOption)
        }
    }, [childrenState,
        currentState?.settingModalData?.[SETTING_FORMS.显示.key],
        currentState?.settingModalData?.x_channel,
        JSON.stringify(currentState?.settingModalData?.y_channel)])

    /**
     * 切换试样，刷新曲线设置菜单
     */
    useEffect(() => {
        if (currentSettingIdFromWidget) {
            dispatch({
                type: UPDATE_SETTING_RES_MENU,
                param: {
                    settingId: currentSettingIdFromWidget,
                    clearSetBreakArrs: false
                }
            })
        }
    }, [
        optSample,
        currentSettingIdFromWidget
    ])

    /**
     * 初始化当前曲线设置
     */
    useEffect(() => {
        if (settingResList && settingResList.length > 0) {
            initStaticCurveById({
                settingId: currentSettingIdFromWidget
            })
        }

        return () => {
            console.log('unmount 曲线页面')
        }
    }, [getProcessID(),
        widgetData,
        settingResList,
        currentSettingIdFromWidget])

    // 校验loading是否结束的effect已移除，现在直接在数据获取完成后更新状态

    // 开始试验初始化
    useEffect(() => {
        if (openExperiment && currentState?.settingModalData?.[SETTING_FORMS.显示.key] === DISPLAY_TYPE.试样) {
            resetData()
        }
    }, [openExperiment,
        currentState?.settingModalData?.[SETTING_FORMS.显示.key]
    ])

    /**
     * 处理重新绘制
     * 1. x轴,y轴切换，如果修改了轴，类型修改重新调用曲线
     * 2.曲线类型切换
     */
    useEffect(() => {
        if (currentState.settingModalData?.x_channel
            && (currentState.settingModalData?.y_channel
                || currentState.settingModalData.y2_channel)
        ) {
            // 优化：移除状态更新，直接清理图表
            historyDataRef.current = {}
            chartRef.current?.clearAllLine?.()
            chartRef.current?.initInterval?.()
        }
    }, [currentState?.settingModalData?.x_channel,
        JSON.stringify(currentState?.settingModalData?.y_channel),
        JSON.stringify(currentState?.settingModalData?.y2_channel),
        currentState?.settingModalData?.buffer_code,
        currentState?.settingModalData?.[SETTING_FORMS['x-偏移'].key],
        currentState?.settingModalData?.[SETTING_FORMS['y-偏移'].key],
        currentState?.settingModalData?.[SETTING_FORMS.显示.key]
    ])

    // 格式标签数据
    useEffect(() => {
        initResultHistoryData()
    }, [resultHistoryData,
        resultsVarsList,
        optSample])

    /**
     * 清空所有数据
     */
    const resetData = () => {
        // 优化：移除状态更新，直接清理图表
        historyDataRef.current = {}
        chartRef.current?.clearAllLine?.()
        chartRef.current?.initInterval?.()
    }

    /**
     * 格式化结果变量数据
     */
    const initResultHistoryData = async () => {
        const tags = resultHistoryData[optSample.code]?.map(tag => {
            const originResultData = resultsVarsList.find(item => item.code === tag.code)
            return { ...originResultData, ...tag }
        })
        if (tags) {
            setTagValues(tags.map(tag => {
                return {
                    ...tag,
                    value: numberFormat(tag.format_type, tag.value === '--' ? 0 : unitConversion(tag.value, tag.dimension_id, tag.unit_id),
                        resultFractionalDigit(tag.format_type, tag.format_info))
                }
            }))
        } else {
            setTagValues([])
        }
    }

    const handleAcceptBreak = useCallback(() => {
        // todo 目前结果变量是用code做key, 断裂点用key
        dispatch({
            type: ACCPET_BREAK_POINT,
            param: {
                settingId: currentSettingIdFromWidget,
                sampleId: optSample.key,
                ...(chartRef.current?.getSelectedPoint?.() ?? {})
            }
        })
        dispatch({
            type: UPDATE_SET_BREAK_FLAG,
            param: {
                breakFlag: false,
                settingId: currentSettingIdFromWidget
            }
        })
    }, [currentSettingIdFromWidget, optSample])

    const handleCreateRes = useCallback(() => {
        const { settingModalData } = currentState
        dispatch({
            type: UPDATE_CURRENT_POINT,
            param: {
                settingId: currentSettingIdFromWidget,
                datas: {
                    sampleId: optSample.code,
                    x: settingModalData.x_channel,
                    y: settingModalData.y_channel,
                    y2: settingModalData?.[SETTING_FORMS.曲线.key]?.length === 2 ? settingModalData.y2_channel : null,
                    ...chartRef.current?.getSelectedPoint?.()
                }
            }
        })
        dispatch({
            type: CREATE_RES_DATA,
            param: {
                settingId: currentSettingIdFromWidget,
                data: {
                    open: true,
                    lineActive: true
                }
            }
        })
    }, [currentState, currentSettingIdFromWidget, optSample])

    const createResModalCallback = () => {
        initProjectHistoryData()
    }

    const setHandMarkingModal = (open) => {
        setMarkingData({
            open
        })
    }
    const markingMessage = (content, type) => {
        const key = '123'
        messageApi.open({
            key,
            type,
            content
        })
    }

    const markingNMessage = (content, msg = t('提示'), type) => {
        const key = '1234'
        nApi.open({
            key,
            msg,
            type,
            description: content
        })
    }
    const handelMarkingOk = (code) => {
        const result = resultData.find(f => f.code === code)
        if (result) {
            if (!(result?.marking_flag ?? false)) {
                markingMessage(t('当前结果变量，未开启手工标记'), 'error')
                return
            }
            if (!(result?.marking_count > 0)) {
                markingMessage(t('当前结果变量，标记点不能小于0'), 'error')
                return
            }
            setMarkingData({
                open: false,
                resultVariableCode: code,
                handMarkingActive: !!code
            })
            const content = (
                <div>
                    <span>
                        {t('当前选择的变量为')}
                        ：
                        {result.variable_name}
                    </span>
                    <br />
                    <span>
                        {t('需要在曲线中做出')}
                        {result?.marking_count ?? 0}
                        {t('个标记点')}
                    </span>
                </div>
            )
            markingNMessage(content, t('提示'), 'success')
        } else {
            markingNMessage(t('未找到结果变量'), 'error')
        }
    }

    // 处理回车函数
    const handleEnter = (point) => {
        if (typeof point !== 'string') {
            switch (chartPointType) {
            // 手工标记
            case SELECT_POINT_TYPES.marking.key:
                const result = resultData.find(f => f.code === currentState?.markingData?.resultVariableCode)
                handelStep(result, point)
                break
                // 创建结果
            case SELECT_POINT_TYPES.createRes.key:
                handleCreateRes()
                break
                // 断裂点
            case SELECT_POINT_TYPES.break.key:
                handleAcceptBreak()
                break
            default:
                console.log('没有找到类型')
                break
            }
        }
    }

    const handelStep = async (result, point) => {
        try {
            const step = currentState?.markingData?.step + 1
            // 执行函数
            if (result.marking_count === 0 || result.marking_count === step) {
                await setInputValue(point, currentState?.markingData?.step)
                const action = actionList.find(f => f?.action_id === result?.marking_action)
                if (action) {
                    await startAction({ action_id: result.marking_action })
                    markingNMessage(`${t('正在执行')}${action.action_name}${t('动作')}`, t('提示'), 'success')
                } else {
                    markingNMessage(t('未找到动作'))
                }
                setMarkingData({
                    step: 0,
                    open: false,
                    resultVariableCode: '',
                    handMarkingActive: false
                })
            } else {
                setMarkingData({
                    step
                })
                markingNMessage(`${t('还需要执行')}${result.marking_count - step}${t('个点')}`)
                await setInputValue(point, currentState?.markingData?.step)
            }
        } catch (error) {
            console.log('handelStep ', error)
        }
    }

    const setInputValue = async (point, step) => {
        // 修改输入变量参数
        if (currentState?.settingModalData?.marking_set) {
            const marking = currentState?.settingModalData?.marking_set?.[step]
            if (marking) {
                const input = inputVariableNumber.find(f => f.code === marking?.input_code)
                if (input) {
                    await batchUpdateInputVar({
                        input_vars: [{
                            id: input.id,
                            default_val: {
                                ...input.default_val,
                                value: Number(point.index)
                            }
                        }]
                    })
                    dispatchSyncInputVar({
                        code: input.code,
                        default_val: {
                            value: Number(point.index)
                        }
                    })
                } else {
                    markingMessage(`${t('未找到第')}${step}${t('步的变量')}`, 'warning')
                }
            }
        }
    }

    const setMarkingData = (markingData) => {
        dispatch({
            type: UPDATE_SET_MARKING_DATA,
            param: {
                markingData,
                settingId: currentSettingIdFromWidget
            }
        })
    }

    // maker移动回调
    const onMakerMouseUp = useCallback((makerTag) => {
        // 如果没有数据更新 则重新赋值状态 重新计算
        if (!makerTag) {
            setRests((rest) => ({ ...rest }))
            return
        }

        const widGetStatus = getWidGetStatus(widgetItem.widget_id) ?? {}
        const tampTag = {
            ...widGetStatus?.data?.tag,
            [makerTag.tagId]: makerTag
        }
        setWidGetStatus({
            widget_id: widgetItem.widget_id,
            data: {
                tag: tampTag
            }
        })
        setRests((rest) => {
            return {
                ...rest,
                tags: rest.tags.map(m => {
                    return {
                        ...m,
                        tags: m.tags.map(tag => {
                            return { ...tag, position: tampTag?.[tag.id] }
                        })
                    }
                })
            }
        })
    }, [])

    /**
     * 导出CSV文件函数
     * 该函数从当前状态中获取图表数据，并根据配置生成CSV文件。
     * 函数会根据曲线配置、标签和单位信息生成表头，并将图表数据填充到相应的表格中。
     * 最后，将生成的数据导出为Excel文件。
     *
     * @returns {void} 无返回值，直接导出文件
     */
    const exportCsv = useCallback(() => {
        // 从当前状态中获取设置模态框数据
        const {
            settingModalData
        } = currentState

        // 获取图表缓存数据，如果不存在则返回空数组
        const allList = chartRef?.current?.getCacheData() || []

        // 从设置模态框数据中解构出曲线配置、标签和单位信息
        const {
            curve_config,
            x_label, y_label, y2_label,
            x_units, y_units, y2_units
        } = settingModalData

        // 将y轴和y2轴标签按'/'分割成数组
        const y_label_list = y_label?.split('/') || []
        const y2_label_list = y2_label?.split('/') || []

        // 初始化表头列表
        const rowHeaderList = []

        // 根据曲线配置生成表头
        if (curve_config?.length === 2) {
            // 如果曲线配置为2，则根据y轴和y2轴标签的最大长度生成表头
            const number = y_label_list?.length > y2_label_list?.length ? y_label_list?.length : y2_label_list?.length
            Array.from({ length: number }).forEach((it, index) => {
                let x = x_label ? `x-${x_label}` : 'x'
                if (x && x !== 'x' && x_units.name) {
                    x = `${x}(${x_units.name})`
                }

                let y = y_label_list?.[index] ? `y1-${y_label_list?.[index]}` : 'y1'
                if (y && y !== 'y1' && y_units?.name) {
                    y = `${y}(${y_units.name})`
                }

                let y2 = y2_label_list?.[index] ? `y2-${y2_label_list?.[index]}` : 'y2'
                if (y2 && y2 !== 'y2' && y2_units?.name) {
                    y2 = `${y2}(${y2_units.name})`
                }

                const item = [x, y, y2]
                rowHeaderList.push(item)
            })
        } else {
            // 如果曲线配置不为2，则仅根据y轴标签生成表头
            y_label_list.forEach((it, index) => {
                let x = x_label ? `x-${x_label}` : 'x'
                if (x && x !== 'x' && x_units.name) {
                    x = `${x}(${x_units.name})`
                }

                let y = y_label_list?.[index] ? `y-${y_label_list?.[index]}` : 'y'
                if (y && y !== 'y' && y_units?.name) {
                    y = `${y}(${y_units.name})`
                }
                const item = [x, y]
                rowHeaderList.push(item)
            })
        }

        // 生成表头信息
        const rowHeader = []
        option.xAxis.forEach(it => {
            if (it.title) {
                rowHeader.push({
                    name: it.title,
                    key: it.key
                })
            }
        })
        option.yAxis.forEach(it => {
            if (it.title) {
                rowHeader.push({
                    name: it.title,
                    key: it.key
                })
            }
        })

        // 生成表格数据列表
        const sheetsDataList = []
        rowHeaderList.forEach((item, index) => {
            const obj = {
                name: `Sheet${index + 1}`,
                data: [item]
            }
            allList?.[index]?.data?.forEach((it) => {
                const x = it?.x ? String(it?.x) : ''
                const y1 = it?.y ? String(it?.y) : ''
                const cData = [
                    x,
                    y1
                ]
                if (curve_config?.length === 2) {
                    const y2 = it?.y2 ? String(it?.y2) : ''
                    cData.push(y2)
                }
                obj.data.push(cData)
            })
            sheetsDataList.push(obj)
        })

        if (sheetsDataList.length > 0) {
            // 导出生成的表格数据为Excel文件
            exportMultipleSheets(sheetsDataList, `静态曲线${moment().format('YYYYMMDDHHmmss')}.xlsx`)
        } else {
            message.error('没有数据可导出')
        }
    }, [currentState])

    return (
        <PageContainer ref={targetRef}>
            {/* loading已改为全局loading管理 */}
            {contextHolder}
            {nContextHolder}
            {/* 曲线图 */}
            <ChartContainer>
                {
                    option && (
                        <StaticChartLine
                            id={layoutCurvetId}
                            ref={chartRef}
                            option={option}
                            handMarkingActive={currentState?.markingData?.handMarkingActive}
                            callBackOriginIndex={callBackOriginIndex}
                            lineIndex={autoIndex}
                            rests={rests}
                            selectPointType={chartPointType}
                            activeID={optSample?.key}
                            onEnter={handleEnter}
                            onMakerMouseUp={onMakerMouseUp}
                            openTag={lineTagShow}
                        />
                    )
                }
            </ChartContainer>

            {/* 右键菜单 */}
            {isRightClick
                && (
                    <ContextMenuComponent
                        acceptBreak={handleAcceptBreak}
                        createRes={handleCreateRes}
                        domId={layoutCurvetId}
                        layoutConfig={layoutConfig}
                        staticCurveSettingId={currentSettingIdFromWidget}
                        exportCsv={exportCsv}
                    />
                )}
            {/* 标签块 */}
            {blockTagShow && (
                <BlockTagDiv
                    staticCurveSettingId={currentSettingIdFromWidget}
                    domId={layoutCurvetId}
                    widgetId={widgetItem.widget_id}
                    isFission={isFission}
                    tagValues={tagValues}
                />
            )}
            {/* 图例 */}
            {currentState.settingModalData?.legend_flag && (
                <LegendDiv
                    staticCurveSettingId={currentSettingIdFromWidget}
                    domId={layoutCurvetId}
                    widgetId={widgetItem.widget_id}
                    isFission={isFission}
                />
            )}

            {/* 创建结果弹窗 */}
            <CreateResModal
                staticCurveSettingId={currentSettingIdFromWidget}
                onCallback={createResModalCallback}
                currentState={currentState}
            />
            {/* 手工标记 */}
            <HandMarkingModal
                open={currentState?.markingData?.open}
                setOpen={setHandMarkingModal}
                onOk={handelMarkingOk}
            />
        </PageContainer>
    )
}

export default StaticCurve
