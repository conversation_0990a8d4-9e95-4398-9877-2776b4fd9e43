import store from '@/redux/store/index'

const findUnitById = (id) => {
    let res

    store.getState().global.unitList.forEach((item) => {
        if (item.units.find(f => f.id === id)) {
            res = item.units.find(f => f.id === id)
        }
    })

    return res
}

const findUnitListByDimensionId = (dimensionId) => {
    return store.getState().global.unitList.find(f => f.id === dimensionId)?.units ?? []
}

export {
    findUnitById,
    findUnitListByDimensionId
}
