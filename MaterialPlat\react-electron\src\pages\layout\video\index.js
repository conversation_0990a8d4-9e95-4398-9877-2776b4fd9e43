/* eslint-disable no-param-reassign */
/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable import/no-extraneous-dependencies */
import React, {
    useState, useRef, useEffect, useMemo, useCallback
} from 'react'
import ContextMenu from '@/components/contextMenu2'
import ReactPlayer from 'react-player'
import { useSelector } from 'react-redux'
import useVideo from '@/hooks/useVideo'
import { useTranslation } from 'react-i18next'
import useWidget from '@/hooks/useWidget'
import debounce from 'lodash/debounce'
import { findItem } from '@/utils/utils'
import { saveWidget, batchUpdateInputVar } from '@/utils/services'
import useNumberInputVariable from '@/hooks/project/inputVariable/useNumberInputVariable'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

import ConfigModal from './components/configModal'
import { ContextMenuContainer, VideoContainer } from './style'

const ContextMenuRightClick = ({
    domId, handelConfig, layoutConfig, isCameraStream, handelCameraStream, isSubTaskSample
}) => {
    const { t, i18n } = useTranslation()
    return (
        <ContextMenuContainer>
            <ContextMenu
                domId={domId}
                layoutConfig={layoutConfig}
            >
                <div
                    className="unique-content"
                    onClick={() => {
                        handelConfig(true)
                    }}
                >
                    {t('编辑')}
                </div>
                <div
                    className={`unique-content ${isSubTaskSample ? 'disabled' : ''}`}
                    onClick={() => {
                        handelCameraStream()
                    }}
                >
                    {!isCameraStream ? t('开启实时摄像头') : t('关闭实时摄像头')}
                </div>

            </ContextMenu>
        </ContextMenuContainer>
    )
}

const Video = ({
    id, data, isHistory = false, item, layoutConfig
}) => {
    const { t, i18n } = useTranslation()

    const videoList = useSelector(state => state.template.videoList)
    const widgetData = useSelector(state => state.template.widgetData)
    const optSample = useSelector(state => state.project.optSample)
    const subTaskSample = useSelector(state => state.subTask.subTaskSample)
    const isFinishMain = useSelector(state => state.subTask.isFinishMain)
    const inputVariableNumber = useNumberInputVariable()
    const {
        getVideoUrl,
        getUserMediaStream,
        stopCamera,
        cameraStream: cameraVideoStream
    } = useVideo()
    const { initWidget } = useWidget()

    const [playerUrl, setPlayerUrl] = useState()
    const [mediaErr, setMediaErr] = useState('')
    const [playing, setPlaying] = useState(false)
    const [controls, setControls] = useState(true)
    const [isCameraStream, setIsCameraStream] = useState(isHistory ? false : !!cameraVideoStream)
    const [configModal, setConfigModal] = useState(false)
    const [configData, setConfigData] = useState()
    const playerRef = useRef()

    useEffect(() => {
        if (item?.widget_id) {
            const findWidget = findItem(widgetData, 'widget_id', item?.widget_id)
            setConfigData(findWidget)
        }
        return () => {
            setConfigData()
        }
    }, [widgetData])

    useEffect(() => {
        if (!isHistory) {
            setIsCameraStream(!!cameraVideoStream)
        }
    }, [cameraVideoStream])

    useEffect(() => {
        if (!subTaskSample && isFinishMain && isCameraStream) {
            handelEnd()
        }
    }, [subTaskSample,
        isFinishMain,
        isCameraStream])

    const handelEnd = useCallback(
        debounce(() => {
            handelStopCamera()
        }, 1000),
        []
    )

    useEffect(() => {
        init()
        return () => {
            setMediaErr('')
            setPlayerUrl()
        }
    }, [data,
        optSample,
        isCameraStream,
        subTaskSample,
        videoList])

    // 自动监听输入变量跳转进度条
    useEffect(() => {
        if (!subTaskSample && configData?.data_source?.input_code) {
            const index = inputVariableNumber?.find(f => f?.code === configData?.data_source?.input_code)?.default_val?.value
            getCreateTimeOriginIndex(index)
        }
    }, [configData?.data_source?.input_code,
        inputVariableNumber,
        subTaskSample])

    // 当前操作的曲线如果十字线动了，就自动移动视频进度条
    const getCreateTimeOriginIndex = async (index) => {
        try {
            if (!isCameraStream) {
                setTimeout(() => {
                    playerRef.current?.seekTo(index)
                }, 500)
            }
        } catch (error) {
            console.log(error)
        }
    }

    /**
     * 优先级
     * 0. 如果有实时视频流，线展示视频流
     * 1. 参数传过来的
     * 2. 实验中，显示实时的视频
     * 3. 最后选中的试样
     * @returns
     */
    const init = async () => {
        if (isCameraStream) {
            startCamera()
            return
        }
        if (data) {
            const res = await getVideoUrl(data)
            if (res) {
                setPlayerUrl(res)
            } else {
                setMediaErr(t('未找到视频源'))
            }
            return
        }

        if (optSample) {
            const video = videoList.find(f => f.sample_code === optSample.code)
            if (video && !subTaskSample) {
                setControls(true)
                setPlaying(false)
                getVideo(video)
            } else if (cameraVideoStream) {
                startCamera()
            } else {
                setMediaErr(t('摄像头关闭'))
            }
        }
    }

    const startCamera = async () => {
        try {
            const cameraStream = await getUserMediaStream()
            setIsCameraStream(true)
            setMediaErr('')
            setPlayerUrl(cameraStream)
            setPlaying(true)
            setControls(false)
        } catch (error) {
            if (error.name === 'UserMediaStream') {
                setMediaErr(t(error.message))
                setIsCameraStream(false)
            }
        }
    }

    const getVideo = async (video) => {
        if (video) {
            const res = await getVideoUrl(video)
            if (res) {
                setPlayerUrl(res)
            } else {
                setMediaErr(t('未找到视频源'))
            }
        }
    }

    const handelConfig = () => {
        setConfigModal(true)
    }

    const configOk = async (value) => {
        // configData.data_source?.input_code
        if (configData) {
            const resWidget = await saveWidget({
                widget_id: configData?.widget_id,
                parent_id: configData?.parent_id,
                widget_type: configData?.widget_type,
                widget_name: configData?.title,
                data_source: { input_code: value }
            })
            if (resWidget) {
                initWidget()
                setConfigModal(false)
            }
        }
    }

    // 进度条监听
    const handleProgress = async (state) => {
        if (playing === false && !subTaskSample) {
            const input = inputVariableNumber.find(f => f.code === configData?.data_source?.input_code)
            if (input && (input.default_val.value ?? 0).toFixed(4) !== Number(state.played).toFixed(4)) {
                subCacheBatchInputVar(input, state)
            }
        }
    }

    const subBatchInputVar = debounce(async (input, state) => {
        dispatchSyncInputVar({
            code: input.code,
            default_val: {
                value: Number(state.played)
            }
        })
        await batchUpdateInputVar({
            input_vars: [{
                id: input.id,
                default_val: {
                    ...input.default_val,
                    value: Number(state.played)
                }
            }]
        })
    }, 1500)

    const subCacheBatchInputVar = useCallback(subBatchInputVar, [])

    const handlePlay = () => {
        setPlaying(true)
    }

    const handlePause = () => {
        setPlaying(false)
    }

    const handelStopCamera = () => {
        if (playerRef.current && playerRef.current.wrapper) {
            const videos = playerRef.current.wrapper.querySelectorAll('video')

            // 对每个 video 元素进行操作
            videos.forEach(video => {
                if (video.srcObject) {
                    video.srcObject = null // 清除 video 元素的 srcObject
                }
            })
        } else {
            console.error('playerRef.current.wrapper 不存在')
        }
        stopCamera()
        setIsCameraStream(false)
    }

    const handelCameraStream = () => {
        if (isCameraStream) {
            handelStopCamera()
        } else {
            startCamera()
        }
    }

    return (
        <VideoContainer>
            {mediaErr
                ? <div>{mediaErr}</div>
                : (
                    <ReactPlayer
                        ref={playerRef}
                        url={playerUrl}
                        width="80%"
                        height="80%"
                        onPlay={handlePlay}
                        onPause={handlePause}
                        controls={controls}
                        playing={playing}
                        onProgress={handleProgress}
                    />
                )}

            <ContextMenuRightClick
                domId={id}
                layoutConfig={layoutConfig}
                handelConfig={handelConfig}
                isCameraStream={isCameraStream}
                handelCameraStream={handelCameraStream}
                isSubTaskSample={!!subTaskSample}
            />
            {configModal
             && (
                 <ConfigModal
                     open={configModal}
                     setOpen={setConfigModal}
                     data={configData?.data_source?.input_code}
                     onOk={configOk}
                 />
             )}

        </VideoContainer>
    )
}

export default Video
