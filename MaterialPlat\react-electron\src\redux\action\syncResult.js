import { PROJECT_RESULT_HISTORY_DATA } from '@/redux/constants/project'

const syncResult = ({
    sampleCode, resultCode, newValue, index, error, errorMessage
}) => {
    return (dispatch, getState) => {
        const currentHistoryData = getState().project.resultHistoryData
        const newHistoryData = Object.fromEntries(
            Object.entries(currentHistoryData).map(([key, value]) => {
                if (key === sampleCode) {
                    const resultIndex = value.findIndex((v) => v.code === resultCode)
                    const newResult = {
                        code: resultCode,
                        error,
                        errorMessage,
                        index,
                        value: newValue
                    }

                    if (resultIndex === -1) {
                        return [key, [...value, newResult]]
                    }

                    return [key, value.map((v, i) => (i === resultIndex ? newResult : v))]
                }
                return [key, value]
            })
        )
        if (!newHistoryData?.[sampleCode]) {
            newHistoryData[sampleCode] = [{
                code: resultCode,
                error,
                errorMessage,
                index,
                value: newValue
            }]
        }

        dispatch({
            type: PROJECT_RESULT_HISTORY_DATA,
            param: newHistoryData
        })
    }
}

export {
    syncResult
}
