import React, { useState, useEffect, memo } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import isEqual from 'lodash/isEqual'
import debounce from 'lodash/debounce'

import useInputVariableList from '@/hooks/project/inputVariable/useInputVariableList'
import { initInputVariables } from '@/redux/action/inputVariables'
import useInputVariables from '@/hooks/project/useInputVariables'
import {
    updateInputVar, getInputVarDetail, submitScript, getResultInfo
} from '@/utils/services'
import { GUIDE_DIALOG_TYPE } from '@/utils/constants'
import SplitHorizontal from '@/components/splitHorizontal/index'
import EmptyIcon from '@/components/emptyIcon/index'
import useCopy from '@/hooks/useCopy'
import SampleForm from '@/pages/dynamicForm/components/SampleForm/index'
import ResultSetting from '@/pages/dynamicForm/components/resultSetting/index'
import VarModal from '@/pages/dialog/inputVariableManage/varModal'
import useInputVariableZmq from '@/hooks/subscribe/useInputVarSubscribe'
import InputRender from '@/module/variableInput/render/index'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

import { dataKey } from '@/pages/dialog/inputVariableManage/varModal/tabProgram/constant'
import ContextMenu from './rightClickMenu'
import { Line, DialogContainer } from './style'

const isSpecialComp = (id) => {
    return Object.values(GUIDE_DIALOG_TYPE).includes(id)
            || id.startsWith(GUIDE_DIALOG_TYPE.线)
            || id.startsWith(GUIDE_DIALOG_TYPE.短线)
}

const Single = ({
    renderParam
}) => {
    return (
        <SplitHorizontal
            sizes={[100]}
        >
            <div className="dynamic-param">
                {
                    renderParam()
                }
            </div>
        </SplitHorizontal>
    )
}

/**
 * 双栏布局组件
 * @param {string} selectedImg - 选中的图片URL
 * @param {Function} renderParam - 渲染参数的函数
 * @param {string} dialogId - 对话框ID
 */
const Double = ({
    selectedImg,
    renderParam,
    dialogId
}) => {
    // 从redux获取结果测试数据
    const resultTestData = useSelector(state => state.template.resultTestData)
    // 从redux获取样本相关数据
    const optSample = useSelector(state => state.project.optSample)
    const sampleList = useSelector(state => state.project.sampleList)

    // 结果图片状态
    const [resultImg, setResultImg] = useState('')

    /**
     * 获取详细信息
     * 防抖处理,300ms内重复调用只执行最后一次
     */
    const getDetail = debounce(async () => {
        // 处理样本参数类型
        if (resultTestData && resultTestData.length === 1 && resultTestData?.[0]?.type === 'SAMPLE_PARAM') {
            const list = sampleList?.find(f => f?.code === optSample?.sample_type)
            const obj = list?.parameters?.find(it => it.parameter_id === resultTestData?.[0]?.id)
            setResultImg(obj?.parameter_img)
        } else if (resultTestData && resultTestData.length === 1 && resultTestData?.[0]?.type === 'RESULT_VAR') {
            // 处理结果变量类型
            const res = await getResultInfo({
                result_variable_id: resultTestData?.[0]?.id
            })
            setResultImg(res?.function_img)
        } else {
            // 清空图片
            setResultImg('')
        }
    }, 300)

    // 监听结果测试数据变化
    useEffect(() => {
        getDetail()
    }, [resultTestData])

    return (
        <SplitHorizontal
            sizes={[72, 28]}
            minSize={window.innerWidth / 10}
        >
            {/* 参数区域 */}
            <div className="dynamic-param">
                {
                    renderParam()
                }
            </div>

            {/* 图片展示区域 */}
            {
                GUIDE_DIALOG_TYPE.结果变量 === dialogId
                    ? (
                        <div className="dynamic-image">
                            {resultImg
                                ? <img src={resultImg} alt="#" />
                                : (
                                    <EmptyIcon />
                                )}
                        </div>
                    ) : (
                        <div className="dynamic-image">
                            {selectedImg
                                ? <img src={selectedImg} alt="#" />
                                : (
                                    <EmptyIcon />
                                )}
                        </div>
                    )
            }

        </SplitHorizontal>
    )
}

const Dialog = ({
    id, layoutConfig, dialogId, showImg = true
}) => {
    const dispatch = useDispatch()

    const { copy } = useCopy()
    const inputVariableList = useInputVariableList()
    const { subCorrelationVariables } = useInputVariables()
    const sampleData = useSelector(state => state.project.sampleData)
    const sampleList = useSelector(state => state.project.sampleList)
    const dialogs = useSelector(state => state.template.dialogs)

    // 对话框数据
    const [dialog, setDialog] = useState()
    // 参数数据
    const [paramItems, setParamItems] = useState([])
    // 当前的参数id
    const [optParamId, setOptParamId] = useState()
    // 当前显示的图片
    const [selectedImg, setSelectedImg] = useState(null)
    // 当前对话框下的脚本数据
    const [scriptData, setScriptData] = useState([])
    // 编辑变量的弹窗
    const [isVarAddModalOpen, setIsVarAddModalOpen] = useState()
    // 可编辑
    const [disabled, setDisabled] = useState(false)
    // 可见性
    const [visible, setVisible] = useState(true)

    useInputVariableZmq({
        code: dialog?.disabled_bind_code,
        callback: (newVal) => {
            setDisabled(!newVal)
        }
    })

    useInputVariableZmq({
        code: dialog?.visible_bind_code,
        callback: (newVal) => {
            setVisible(newVal)
        }
    })

    // 先清空
    useEffect(() => {
        setDisabled(false)
        setVisible(true)
        setOptParamId()
        setSelectedImg()
    }, [dialogId])

    useEffect(() => {
        initParams()
    }, [dialogId, dialogs, inputVariableList])

    useEffect(() => {
        init()
    }, [dialogId, dialogs])

    const initParams = () => {
        const currentDialog = dialogs.find(i => i.dialog_id === dialogId)
        setDialog(currentDialog)
        const params = currentDialog?.variable_ids
            ?.map(varId => {
                if (isSpecialComp(varId)) {
                    return { id: varId }
                }
                return inputVariableList.find(i => i.id === varId)
            })
            .filter(Boolean)

        setParamItems(params)

        // 如果更新了脚本重新走脚本
        if (paramItems
            && paramItems.length > 0
            && !isEqual(paramItems?.flatMap(m => Object.values(m?.program_tab ?? {})), params?.flatMap(m => Object.values(m.program_tab ?? {})))) {
            init()
        }

        // 更新输入变量，重新初始化图片
        const optParams = params?.find(f => f.id === optParamId)
        if (params?.length > 0 && optParams) {
            initSelectedImg(optParams)
        }
    }

    // 对话框触发一次脚本
    const init = async () => {
        const currentDialog = dialogs.find(i => i.dialog_id === dialogId)
        if (currentDialog?.variable_ids) {
            const res = await getInputVarDetail({ ids: currentDialog?.variable_ids })

            // 触发脚本
            handelScript(res)

            // 默认选中第一个参数并显示图片
            if (res?.length > 0) {
                setOptParamId(res?.[0]?.id)
                initSelectedImg(res?.[0])
            }
        }
    }

    // 设置默认图片
    const initSelectedImg = (param) => {
        if ([GUIDE_DIALOG_TYPE.试样参数, GUIDE_DIALOG_TYPE.试样不带参数].includes(param.id)) {
            setSelectedImg(sampleList?.find(f => f?.sample_id === sampleData[0]?.children[0]?.data[0]?.sample_id)?.img)
        }
        if ([GUIDE_DIALOG_TYPE.结果变量].includes(param.id)) {
            setSelectedImg()
        }
        if ('pic' in param) {
            setSelectedImg(param?.pic)
        }
    }

    const handelScript = async (data) => {
        const res = await subCorrelationVariables(data)
        setScriptData(res)
    }

    // 修改input接口
    const updateInput = async (data) => {
        try {
            // 先改redux 同步ui 改db的mq消息 判断没有变化就过滤掉
            dispatchSyncInputVar({ code: data.code }, data)
            // 改db
            const res = await updateInputVar(data)
            if (res) {
                handelScript(paramItems)

                // 对话框中输入变量变化时触发脚本
                onTriggerScript(data, dataKey.onChange)
            }
        } catch (error) {
            console.log(error)
        }
    }

    const renderDialogSpecialComp = (paramId, index) => {
        if (paramId.startsWith(GUIDE_DIALOG_TYPE.线) || paramId.startsWith(GUIDE_DIALOG_TYPE.短线)) {
            return (
                <Line isShort={paramId.startsWith(GUIDE_DIALOG_TYPE.短线)} />
            )
        }

        switch (paramId) {
        case GUIDE_DIALOG_TYPE.试样参数:
        case GUIDE_DIALOG_TYPE.试样不带参数:
            return (
                <SampleForm
                    isBase={paramId === GUIDE_DIALOG_TYPE.试样不带参数}
                    isSelected={optParamId === paramId}
                    onCallBackImg={setSelectedImg}
                />
            )
        case GUIDE_DIALOG_TYPE.结果变量:
            return <ResultSetting />
        default:
            return <></>
        }
    }

    const handleParamEdit = () => {
        setIsVarAddModalOpen(true)
    }

    const handleVarAddOk = () => {
        // 初始化输入变量，重新获取脚本
        dispatch(initInputVariables())
        setIsVarAddModalOpen(false)
    }

    const handleVarAddModalCancel = () => {
        setIsVarAddModalOpen(false)
    }

    // 触发对应程序类型的脚本
    const onTriggerScript = (data, type) => {
        const script = data.program_tab[type]
        if (script) {
            submitScript({
                script,
                result_type: 'BOOL'
            })
        }
    }

    const renderParam = () => {
        return (
            <>
                {
                    paramItems?.map((i, index) => (
                        <div
                            key={i.id}
                            onMouseUp={e => {
                                // e.stopPropagation()
                                setOptParamId(i.id)
                            }}
                            onDoubleClick={() => i?.code && copy(i?.code)}
                            className={optParamId === i.id ? 'selected' : ''}
                            onClick={() => {
                                setOptParamId(i.id)

                                // 输入变量直接拿图片
                                if (!isSpecialComp(i.id)) {
                                    setSelectedImg(i.pic)
                                }
                            }}
                        >
                            {
                                isSpecialComp(i.id)
                                    ? renderDialogSpecialComp(i.id, index)
                                    : (
                                        <InputRender
                                            variable={i}
                                            scriptData={scriptData}
                                            onChange={updateInput}
                                            onTriggerScript={(type) => onTriggerScript(i, type)}
                                            openMarginBottom
                                        />
                                    )
                            }
                        </div>
                    ))
                }
            </>
        )
    }

    if (!visible) {
        return <></>
    }

    return (
        <DialogContainer>
            {/* 可编辑的蒙板 */}
            {
                disabled && (
                    <div
                        className="block_mask"
                        style={{
                            background: `rgba(0,0,0,${dialog?.mask_opacity ?? 0.3})`
                        }}
                    />
                )
            }
            {/* 两种布局 */}
            {
                (showImg && dialog?.is_show_img)
                    ? (
                        <Double
                            dialogId={dialogId}
                            selectedImg={selectedImg}
                            renderParam={renderParam}
                        />
                    )
                    : (
                        <Single
                            renderParam={renderParam}
                        />
                    )

            }

            {/* 编辑变量 */}
            {
                isVarAddModalOpen && (
                    <VarModal
                        editId={optParamId}
                        mode="edit"
                        open={isVarAddModalOpen}
                        onOk={handleVarAddOk}
                        onCancel={handleVarAddModalCancel}
                    />
                )
            }

            {/* 右键弹窗 */}
            <ContextMenu
                domId={id}
                showMenu={optParamId && !isSpecialComp(optParamId)}
                layoutConfig={layoutConfig}
                handleClick={handleParamEdit}
            />
        </DialogContainer>
    )
}

export default memo(Dialog)
