2025-09-08 09:16:26,478 [main] INFO  jdbc.sqlonly - PRAGMA cache_size = -16000 
 
2025-09-08 09:16:26,481 [main] INFO  jdbc.sqlonly - PRAGMA journal_mode = WAL 
 
2025-09-08 09:16:26,491 [main] INFO  jdbc.sqlonly - PRAGMA synchronous = NORMAL 
 
2025-09-08 09:16:26,491 [main] INFO  jdbc.sqlonly - PRAGMA temp_store = MEMORY 
 
2025-09-08 09:16:26,492 [main] INFO  jdbc.sqlonly - PRAGMA mmap_size = 134217728 
 
2025-09-08 09:16:26,492 [main] INFO  jdbc.sqlonly - PRAGMA page_size = 4096 
 
2025-09-08 09:16:26,556 [main] INFO  jdbc.sqlonly - SELECT * FROM t_system_version WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:16:26,601 [chime-1] INFO  jdbc.sqlonly - SELECT item.*, lastInspect.inspect_time as last_inspect_time, lastInspectRecord.limit_inspect_time 
as last_limit_inspect_time, device.inspection_device_type_id, device.name as inspection_device_type_name, 
inspection_frequency_days, inspection_remind_days FROM t_inspection_item item left join ( select 
record.inspection_item_id, max(inspect_time) as inspect_time from t_inspection_record record 
INNER JOIN t_inspection_item item on item.delete_flag = 0 and item.inspection_item_id = record.inspection_item_id 
where (record.status != 'changed' or record.status is null) and record.delete_flag = 0 GROUP 
BY record.inspection_item_id ) lastInspect on lastInspect.inspection_item_id = item.inspection_item_id 
left join ( select record.inspection_item_id, max(record.created_time) as created_time, max(record.limit_inspect_time) 
as limit_inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on 
item.delete_flag = 0 and item.inspection_item_id = record.inspection_item_id where (record.status 
!= 'changed' or record.status is null) and record.delete_flag = 0 GROUP BY record.inspection_item_id 
) lastInspectRecord on lastInspectRecord.inspection_item_id = item.inspection_item_id INNER 
JOIN t_inspection_device_type device on device.inspection_device_type_id = item.inspection_device_type_id 
and device.delete_flag = 0 where item.delete_flag = 0 ; 
 
2025-09-08 09:16:26,623 [chime-2] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference >= 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
2025-09-08 09:16:26,626 [chime-2] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference < 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
2025-09-08 09:16:31,488 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 09:16:31,804 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_ccss WHERE delete_flag = false ; 
 
2025-09-08 09:16:31,805 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_ccss WHERE delete_flag = false ; 
 
2025-09-08 09:16:31,805 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_axis WHERE delete_flag = false ; 
 
2025-09-08 09:16:31,808 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_channel WHERE delete_flag = false ; 
 
2025-09-08 09:16:31,817 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_device WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,075 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_sys_user WHERE delete_flag = 0 AND account = 'admin' AND password = '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918' 
 
2025-09-08 09:16:32,076 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT power,name,used_templates FROM t_sys_role WHERE delete_flag = 0 AND id = 1 
 
2025-09-08 09:16:32,078 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT tsu.id,tsu.name,tsu.role_id,tsu.account,tsr.name as role_name FROM t_sys_user tsu LEFT 
JOIN t_sys_role tsr ON tsu.role_id = tsr.id WHERE tsu.delete_flag = false AND tsu.id = 1; 
 
2025-09-08 09:16:32,101 [qtp1566353334-135] INFO  jdbc.sqlonly - INSERT INTO t_log (content) VALUES ( '【超级管理员】admin: 登录') ; 
 
2025-09-08 09:16:32,178 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_station_or_home_layout_config WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:16:32,228 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 09:16:32,232 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 09:16:32,342 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT DISTINCT language_name FROM t_international WHERE delete_flag = false AND language_name 
IS NOT null; 
 
2025-09-08 09:16:32,372 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT id, project_directory FROM t_system_config WHERE 1 = 1 AND id = '1' LIMIT 1; ; 
 
2025-09-08 09:16:32,399 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_module_datasource WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,427 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT audio_id, audio_name, audio_type, remark, name_file FROM t_audio WHERE delete_flag = 
false ; 
 
2025-09-08 09:16:32,454 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,487 [qtp1566353334-133] INFO  jdbc.sqlonly - SELECT id,name,code,default_unit_id,created_user_id,delete_flag FROM t_units_dimension WHERE 
delete_flag = false ORDER BY created_time; 
 
2025-09-08 09:16:32,496 [qtp1566353334-133] INFO  jdbc.sqlonly - SELECT tu.id,tu.name,tu.code,tu.dimension_id,tu.created_user_id, tu.order_num,tu.visible,tu.proportion,tu.standard_id,tu.delete_flag, 
tus.name standard,tud.name dimension FROM t_units tu LEFT JOIN t_units_standard tus ON tu.standard_id 
= tus.id LEFT JOIN t_units_dimension tud ON tu.dimension_id = tud.id WHERE tu.delete_flag = 
false ; 
 
2025-09-08 09:16:32,511 [qtp1566353334-139] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference >= 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
2025-09-08 09:16:32,513 [qtp1566353334-139] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference < 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
2025-09-08 09:16:32,533 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,537 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:16:32,562 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 09:16:32,565 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 09:16:32,594 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 09:16:32,597 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:16:32,625 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_ccss WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,627 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_ccss WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,628 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_axis WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,629 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_channel WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,639 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_device WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,683 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_station_project WHERE 1 = 1 AND delete_flag = 0 AND station_id = 'global-monitoring-manager' 
LIMIT 1; ; 
 
2025-09-08 09:16:32,740 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE delete_flag = false ; 
 
2025-09-08 09:16:32,766 [qtp1566353334-135] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 09:16:33,347 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 09:16:33,348 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:16:33,361 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 09:16:33,363 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 09:16:33,379 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 09:16:33,382 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:16:34,071 [qtp1566353334-138] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 09:16:34,101 [qtp1566353334-166] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 09:16:37,242 [qtp1566353334-138] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 09:16:37,269 [qtp1566353334-166] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 09:16:55,307 [qtp1566353334-163] INFO  jdbc.sqlonly - INSERT INTO t_project_db (remark,project_prefix,updated_user_id,project_state,industry,is_imported,template_type,standard_number,updated_time,entrust_number,is_extrapolation_project,project_version,temporary_flag,project_directory,entrust_unit,delete_flag,materials,version,template_name,experiment_type,project_name,created_user_id) 
VALUES ( NULL, *************, NULL, NULL, '9', 1, '5', '08', '2025-09-07 15:50:42', NULL, 0, 
NULL, 0, 'D:/WorkProject/ZJ/MaterialPlat/clj-backend/db', NULL, 0, '10', '**************', 
'K1C试验模板0907', '1', 'K1C试验项目0907', 1) ; 
 
2025-09-08 09:16:55,308 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 ORDER BY project_id DESC LIMIT 1; ; 
 
2025-09-08 09:16:55,308 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT tsu.id,tsu.name,tsu.role_id,tsu.account,tsr.name as role_name FROM t_sys_user tsu LEFT 
JOIN t_sys_role tsr ON tsu.role_id = tsr.id WHERE tsu.delete_flag = false AND tsu.id = 1; 
 
2025-09-08 09:16:55,309 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:16:55,312 [qtp1566353334-163] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '【超级管理员】admin: 
导入模板或项目') ; 
 
2025-09-08 09:16:55,339 [qtp1566353334-138] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 09:16:55,392 [qtp1566353334-163] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 09:17:58,324 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false AND station_id = '2d06ec3a-9ecd-422c-a76a-1afc16b47b24' 
; 
 
2025-09-08 09:17:58,327 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 AND id = '2d06ec3a-9ecd-422c-a76a-1afc16b47b24' LIMIT 1; 
; 
 
2025-09-08 09:17:58,362 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:17:58,364 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA cache_size = -16000 
 
2025-09-08 09:17:58,364 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA journal_mode = WAL 
 
2025-09-08 09:17:58,364 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA synchronous = NORMAL 
 
2025-09-08 09:17:58,364 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA temp_store = MEMORY 
 
2025-09-08 09:17:58,364 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA mmap_size = 134217728 
 
2025-09-08 09:17:58,365 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA page_size = 4096 
 
2025-09-08 09:17:58,365 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA auto_vacuum = INCREMENTAL 
 
2025-09-08 09:17:58,367 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:17:58,369 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE 1 = 1 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' 
LIMIT 1; ; 
 
2025-09-08 09:17:58,412 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:17:58,414 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE 1 = 1 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' 
LIMIT 1; ; 
 
2025-09-08 09:17:58,428 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 09:17:58,430 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_station_project WHERE 1=1 AND delete_flag = 0 AND station_id = '2d06ec3a-9ecd-422c-a76a-1afc16b47b24' 
AND project_id = 31 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' ; 
 
2025-09-08 09:17:58,431 [qtp1566353334-167] INFO  jdbc.sqlonly - INSERT INTO t_station_project (station_id,cfg_id,project_id,id,created_user_id) VALUES ( '2d06ec3a-9ecd-422c-a76a-1afc16b47b24', 
'0ae471aa-4442-42b2-922c-4fc6ba916b10', 31, 'fcdb769c-ef88-4e8b-a797-241f814398bf', 1) ; 
 
2025-09-08 09:17:58,456 [qtp1566353334-167] INFO  jdbc.sqlonly - UPDATE t_mapping SET mapping_context = '{"_hw_device":null,"_servo_axis_sensor":[{"ctrl_cmd_flag":1,"_info_signals":{"_block_line":{"signal_variable_id":41},"_cycles":{"signal_variable_id":6},"_command":{"signal_variable_id":13},"_in_signals":{"signal_variable_id":42},"_timer":{"signal_variable_id":4},"_cmd_frequency":{"signal_variable_id":7}},"children":[{"min":null,"children":null,"unit":null,"name":"\u4f4d\u79fb","init_value":null,"parent_type":"AXIS","type":"AD","parent_ccss_id":null,"variable_name":"\u5df2\u9009\u62e9","channel_id":"15d8d6ca-bea3-457c-b34c-4816a0ae8332","parent_axis_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","max":null,"parent_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","id":"15d8d6ca-bea3-457c-b34c-4816a0ae8332","unit_type":null,"delete_flag":0,"unmatched":false,"signal_variable_id":{"min":12,"max":11,"current":1}},{"min":null,"children":null,"unit":null,"name":"\u8d1f\u8377","init_value":null,"parent_type":"AXIS","type":"AD","parent_ccss_id":null,"variable_name":"\u5df2\u9009\u62e9","channel_id":"0afa6632-42ac-4647-9242-768a47e2ee7f","parent_axis_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","max":null,"parent_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","id":"0afa6632-42ac-4647-9242-768a47e2ee7f","unit_type":null,"delete_flag":0,"unmatched":false,"signal_variable_id":{"min":18,"max":17,"current":2}},{"min":null,"children":null,"unit":null,"name":"\u53d8\u5f62","init_value":null,"parent_type":"AXIS","type":"AD","parent_ccss_id":null,"variable_name":"\u5df2\u9009\u62e9","channel_id":"be1851be-34b7-46e4-96af-8e3202e894c1","parent_axis_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","max":null,"parent_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","id":"be1851be-34b7-46e4-96af-8e3202e894c1","unit_type":null,"delete_flag":0,"unmatched":false,"signal_variable_id":{"min":20,"max":19,"current":3}}],"ctrl_block_cycles_flag":1,"cmd_frequency_flag":1,"name":"\u4f3a\u670d1","parent_type":"ServoAxisSensor","axis_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","type":"ServoAxisSensor","upper_limits_flag":1,"axis_name":"\u4f3a\u670d1","channel_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","ctrl_output_flag":1,"parent_id":"","id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","ctrl_cycles_flag":1,"delete_flag":0,"block_line_flag":1,"ad_sensor_count":3,"lower_limits_flag":1,"ctrl_maxmin_flag":1,"unmatched":false,"old_type":"ServoAxisSensor"}],"_output":[],"_temp_axis_sensor":[],"_creep_axis_sensor":[],"_hand_box":[],"_a_d":[],"_d_a":[],"virtual_channel":[{"hw_key":"hardware_simulator","name":"\u4f4d\u79fb\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u4f4d\u79fb\u6700\u5927\u503c","channel_id":5,"parent_axis_id":"","parent_id":"b5f23468-d9b9-4c4d-a2c4-74f074a32ef1","id":5,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":5,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u4f4d\u79fb\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u4f4d\u79fb\u6700\u5c0f\u503c","channel_id":10,"parent_axis_id":"","parent_id":"d405beaa-76f0-408d-bd91-62087a7d8f6d","id":10,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":10,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u5468\u671f","parent_type":"AXIS","variable_name":"\u5468\u671f-\u5468\u671f","channel_id":14,"parent_axis_id":"","parent_id":"8f1cf524-c604-4abd-89bb-96a67ee67bb0","id":14,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":14,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u65f6\u95f4","parent_type":"AXIS","variable_name":"\u5468\u671f-\u65f6\u95f4","channel_id":15,"parent_axis_id":"","parent_id":"5bd195c2-250e-4d74-80c2-f49332479064","id":15,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":15,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u88c2\u7eb9\u957f\u5ea6","channel_id":16,"parent_axis_id":"","parent_id":"5bd195c2-250e-4d74-80c2-f49332479064","id":16,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":16,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u6700\u5927\u503c","channel_id":8,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":8,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":8,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u6700\u5c0f\u503c","channel_id":9,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":9,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":9,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5e73\u5747\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u5e73\u5747\u88c2\u7eb9\u957f\u5ea6","channel_id":21,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":21,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":21,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u75b2\u52b3\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u75b2\u52b3\u88c2\u7eb9\u957f\u5ea6","channel_id":22,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":22,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":22,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","channel_id":23,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":23,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":23,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u540e\u9636\u6bb5\u7684\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u6700\u540e\u9636\u6bb5\u7684\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","channel_id":24,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":24,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":24,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u7ec8\u6700\u5927\u529b","parent_type":"AXIS","variable_name":"\u6700\u7ec8\u6700\u5927\u529b","channel_id":25,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":25,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":25,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u9891\u7387","parent_type":"AXIS","variable_name":"\u5468\u671f-\u9891\u7387","channel_id":26,"parent_axis_id":"","parent_id":"2e6f3899-e4f8-4659-9ccc-b43c277301f2","id":26,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":26,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5f62\u72b6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u5f62\u72b6\u56e0\u5b50","channel_id":27,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":27,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":27,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u6700\u5927\u8d1f\u8377","channel_id":28,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":28,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":28,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5c0f\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u6700\u5c0f\u8d1f\u8377","channel_id":29,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":29,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":29,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u8303\u56f4","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u8303\u56f4","channel_id":30,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":30,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":30,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5c0fK","parent_type":"AXIS","variable_name":"\u6700\u5c0fK","channel_id":32,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":32,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":32,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927K","parent_type":"AXIS","variable_name":"\u6700\u5927K","channel_id":31,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":31,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":31,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"K\u503c\u8303\u56f4","parent_type":"AXIS","variable_name":"K\u503c\u8303\u56f4","channel_id":33,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":33,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":33,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8bd5\u9a8c\u8fdb\u5ea6","parent_type":"AXIS","variable_name":"\u8bd5\u9a8c\u8fdb\u5ea6","channel_id":34,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":34,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":34,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u67d4\u5ea6","parent_type":"AXIS","variable_name":"\u67d4\u5ea6","channel_id":35,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":35,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":35,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u62df\u5408\u4e0a\u9650\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u62df\u5408\u4e0a\u9650\u8d1f\u8377","channel_id":36,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":36,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":36,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u62df\u5408\u4e0b\u9650\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u62df\u5408\u4e0b\u9650\u8d1f\u8377","channel_id":37,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":37,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":37,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u865a\u62df\u5468\u671f","parent_type":"AXIS","variable_name":"\u865a\u62df\u5468\u671f","channel_id":38,"parent_axis_id":"","parent_id":"e18a3dd7-c76e-4f3d-a339-958f8ff08ec9","id":38,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":38,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u53d8\u5f62\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u53d8\u5f62\u6700\u5c0f\u503c","channel_id":39,"parent_axis_id":"","parent_id":"61bb339b-ca15-4dce-a592-70c71903d90b","id":39,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":39,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u53d8\u5f62\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u53d8\u5f62\u6700\u5927\u503c","channel_id":40,"parent_axis_id":"","parent_id":"61bb339b-ca15-4dce-a592-70c71903d90b","id":40,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":40,"virtual_parent_type":"virtualChannel"}],"_input":[]}',usable_resource 
= '{"_hw_device":[],"_servo_axis_sensor":[{"_info_signals":{"_block_line":{"signal_variable_id":41},"_cycles":{"signal_variable_id":6},"_command":{"signal_variable_id":13},"_in_signals":{"signal_variable_id":42},"_timer":{"signal_variable_id":4},"_cmd_frequency":{"signal_variable_id":7}},"hw_key":"hardware_simulator","form_name":"\u4f3a\u670d\u8f742(hardware_simulator)","children":[{"hw_key":"hardware_simulator","min":0,"form_name":"\u4f4d\u79fb","page_id":"1","unit":null,"name":"\u4f4d\u79fb","sensor_name":["Sensor0"],"init_value":null,"parent_type":"AXIS","type":"AD","sensor_count":1,"parent_ccss_id":"hardware_simulator-0000-hw-ccss-id-0000","channel_id":"7189e3ae-78c0-42af-8cb3-5b1abedc0247","parent_axis_id":"7853d594-6516-461f-aa64-0c783f9f60e5","daq_rate":1000,"max":0,"id":"7189e3ae-78c0-42af-8cb3-5b1abedc0247","unit_type":"","delete_flag":0,"idx":0,"signal_variable_id":{"min":12,"max":11,"current":1}},{"hw_key":"hardware_simulator","min":0,"form_name":"\u8d1f\u8377","page_id":"1","unit":null,"name":"\u8d1f\u8377","sensor_name":["\u4f20\u611f\u56681"],"init_value":null,"parent_type":"AXIS","type":"AD","sensor_count":1,"parent_ccss_id":"hardware_simulator-0000-hw-ccss-id-0000","channel_id":"80f17c18-4765-4170-8e9d-6b60d416a6c2","parent_axis_id":"7853d594-6516-461f-aa64-0c783f9f60e5","daq_rate":1000,"max":0,"id":"80f17c18-4765-4170-8e9d-6b60d416a6c2","unit_type":"","delete_flag":0,"idx":1,"signal_variable_id":{"min":18,"max":17,"current":2}},{"hw_key":"hardware_simulator","min":0,"form_name":"\u53d8\u5f62","page_id":"1","unit":null,"name":"\u53d8\u5f62","sensor_name":["\u4f20\u611f\u56682"],"init_value":null,"parent_type":"AXIS","type":"AD","sensor_count":1,"parent_ccss_id":"hardware_simulator-0000-hw-ccss-id-0000","channel_id":"4705269f-1dee-4371-a250-265d5a586971","parent_axis_id":"7853d594-6516-461f-aa64-0c783f9f60e5","daq_rate":1000,"max":0,"id":"4705269f-1dee-4371-a250-265d5a586971","unit_type":"","delete_flag":0,"idx":2,"signal_variable_id":{"min":20,"max":19,"current":3}}],"ctrl_block_cycles_flag":1,"cmd_frequency_flag":1,"name":"\u4f3a\u670d\u8f740","sensor_name":[],"parent_type":"ServoAxisSensor","axis_id":"7853d594-6516-461f-aa64-0c783f9f60e5","type":"ServoAxisSensor","upper_limits_flag":1,"press_up_or_down":1,"tensile_up_or_down":1,"axis_name":"\u4f3a\u670d\u8f740","daq_rate":0,"parent_id":"hardware_simulator-0000-hw-ccss-id-0000","id":"5d692fd6-f30c-4ab9-b91e-f5436d38d1d8","up_direct_value":1,"ctrl_cycles_flag":1,"delete_flag":0,"block_line_flag":1,"ad_sensor_count":10,"idx":0,"lower_limits_flag":1,"signal_variable_id":null}],"_output":[],"_temp_axis_sensor":[],"_creep_axis_sensor":[],"_hand_box":[],"_a_d":[],"_d_a":[],"virtual_channel":[{"hw_key":"hardware_simulator","name":"\u4f4d\u79fb\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u4f4d\u79fb\u6700\u5927\u503c","channel_id":5,"parent_axis_id":"","parent_id":"b5f23468-d9b9-4c4d-a2c4-74f074a32ef1","id":5,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":5,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u4f4d\u79fb\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u4f4d\u79fb\u6700\u5c0f\u503c","channel_id":10,"parent_axis_id":"","parent_id":"d405beaa-76f0-408d-bd91-62087a7d8f6d","id":10,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":10,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u5468\u671f","parent_type":"AXIS","variable_name":"\u5468\u671f-\u5468\u671f","channel_id":14,"parent_axis_id":"","parent_id":"8f1cf524-c604-4abd-89bb-96a67ee67bb0","id":14,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":14,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u65f6\u95f4","parent_type":"AXIS","variable_name":"\u5468\u671f-\u65f6\u95f4","channel_id":15,"parent_axis_id":"","parent_id":"5bd195c2-250e-4d74-80c2-f49332479064","id":15,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":15,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u88c2\u7eb9\u957f\u5ea6","channel_id":16,"parent_axis_id":"","parent_id":"5bd195c2-250e-4d74-80c2-f49332479064","id":16,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":16,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u6700\u5927\u503c","channel_id":8,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":8,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":8,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u6700\u5c0f\u503c","channel_id":9,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":9,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":9,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5e73\u5747\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u5e73\u5747\u88c2\u7eb9\u957f\u5ea6","channel_id":21,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":21,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":21,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u75b2\u52b3\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u75b2\u52b3\u88c2\u7eb9\u957f\u5ea6","channel_id":22,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":22,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":22,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","channel_id":23,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":23,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":23,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u540e\u9636\u6bb5\u7684\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u6700\u540e\u9636\u6bb5\u7684\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","channel_id":24,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":24,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":24,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u7ec8\u6700\u5927\u529b","parent_type":"AXIS","variable_name":"\u6700\u7ec8\u6700\u5927\u529b","channel_id":25,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":25,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":25,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u9891\u7387","parent_type":"AXIS","variable_name":"\u5468\u671f-\u9891\u7387","channel_id":26,"parent_axis_id":"","parent_id":"2e6f3899-e4f8-4659-9ccc-b43c277301f2","id":26,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":26,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5f62\u72b6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u5f62\u72b6\u56e0\u5b50","channel_id":27,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":27,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":27,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u6700\u5927\u8d1f\u8377","channel_id":28,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":28,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":28,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5c0f\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u6700\u5c0f\u8d1f\u8377","channel_id":29,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":29,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":29,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u8303\u56f4","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u8303\u56f4","channel_id":30,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":30,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":30,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5c0fK","parent_type":"AXIS","variable_name":"\u6700\u5c0fK","channel_id":32,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":32,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":32,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927K","parent_type":"AXIS","variable_name":"\u6700\u5927K","channel_id":31,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":31,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":31,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"K\u503c\u8303\u56f4","parent_type":"AXIS","variable_name":"K\u503c\u8303\u56f4","channel_id":33,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":33,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":33,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8bd5\u9a8c\u8fdb\u5ea6","parent_type":"AXIS","variable_name":"\u8bd5\u9a8c\u8fdb\u5ea6","channel_id":34,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":34,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":34,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u67d4\u5ea6","parent_type":"AXIS","variable_name":"\u67d4\u5ea6","channel_id":35,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":35,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":35,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u62df\u5408\u4e0a\u9650\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u62df\u5408\u4e0a\u9650\u8d1f\u8377","channel_id":36,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":36,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":36,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u62df\u5408\u4e0b\u9650\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u62df\u5408\u4e0b\u9650\u8d1f\u8377","channel_id":37,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":37,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":37,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u865a\u62df\u5468\u671f","parent_type":"AXIS","variable_name":"\u865a\u62df\u5468\u671f","channel_id":38,"parent_axis_id":"","parent_id":"e18a3dd7-c76e-4f3d-a339-958f8ff08ec9","id":38,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":38,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u53d8\u5f62\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u53d8\u5f62\u6700\u5c0f\u503c","channel_id":39,"parent_axis_id":"","parent_id":"61bb339b-ca15-4dce-a592-70c71903d90b","id":39,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":39,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u53d8\u5f62\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u53d8\u5f62\u6700\u5927\u503c","channel_id":40,"parent_axis_id":"","parent_id":"61bb339b-ca15-4dce-a592-70c71903d90b","id":40,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":40,"virtual_parent_type":"virtualChannel"}],"_input":[]}',mapping_status 
= 'NORMAL',cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' ; 
 
2025-09-08 09:17:58,492 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 09:17:58,493 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 09:17:58,505 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:17:58,533 [qtp1566353334-135] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 09:17:58,558 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 09:17:58,561 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:17:58,583 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 09:17:58,585 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:17:58,611 [qtp1566353334-134] INFO  jdbc.sqlonly - INSERT INTO t_log (content) VALUES ( '打开项目 project_31') ; 
 
2025-09-08 09:17:58,613 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:17:58,645 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:17:58,670 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_station_project WHERE 1=1 AND project_id = 31 AND station_id != 'global-monitoring-manager' 
ORDER BY created_time DESC ; 
 
2025-09-08 09:17:58,672 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE delete_flag = false AND parent_group = '0' ; 
 
2025-09-08 09:17:58,674 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND id != '2' AND parent_group 
!= '0' ; 
 
2025-09-08 09:17:58,677 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_result WHERE delete_flag = false ; 
 
2025-09-08 09:17:58,680 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id, code FROM t_units WHERE 1=1 ; 
 
2025-09-08 09:17:58,683 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id, code FROM t_units_dimension WHERE 1=1 ; 
 
2025-09-08 09:17:58,685 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT vi.*, sv.variable_val FROM t_variable_input vi LEFT JOIN t_sample_variable sv ON sv.variable_code 
= vi.code WHERE vi.delete_flag = false AND sv.delete_flag = false AND sv.sample_code = 'sample_14785d372'; 
 
2025-09-08 09:17:58,738 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id, code FROM t_units WHERE 1=1 ; 
 
2025-09-08 09:17:58,741 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id, code FROM t_units_dimension WHERE 1=1 ; 
 
2025-09-08 09:17:58,742 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_input WHERE 1=1 AND is_overall = 1 ; 
 
2025-09-08 09:17:58,746 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:17:58,747 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT signal_variable_id, code FROM t_variable_signal WHERE delete_flag = false ; 
 
2025-09-08 09:17:58,750 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE 1 = 1 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' 
LIMIT 1; ; 
 
2025-09-08 09:17:58,751 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 AND id = '2d06ec3a-9ecd-422c-a76a-1afc16b47b24' LIMIT 1; 
; 
 
2025-09-08 09:17:58,755 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:17:58,756 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:17:58,761 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 41 
LIMIT 1; ; 
 
2025-09-08 09:17:58,762 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 6 
LIMIT 1; ; 
 
2025-09-08 09:17:58,762 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 13 
LIMIT 1; ; 
 
2025-09-08 09:17:58,763 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 42 
LIMIT 1; ; 
 
2025-09-08 09:17:58,764 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 4 
LIMIT 1; ; 
 
2025-09-08 09:17:58,764 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 7 
LIMIT 1; ; 
 
2025-09-08 09:17:58,765 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 1 
LIMIT 1; ; 
 
2025-09-08 09:17:58,767 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 12 
LIMIT 1; ; 
 
2025-09-08 09:17:58,769 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 11 
LIMIT 1; ; 
 
2025-09-08 09:17:58,769 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 2 
LIMIT 1; ; 
 
2025-09-08 09:17:58,770 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 18 
LIMIT 1; ; 
 
2025-09-08 09:17:58,771 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 17 
LIMIT 1; ; 
 
2025-09-08 09:17:58,771 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 3 
LIMIT 1; ; 
 
2025-09-08 09:17:58,772 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 20 
LIMIT 1; ; 
 
2025-09-08 09:17:58,772 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 19 
LIMIT 1; ; 
 
2025-09-08 09:17:58,774 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_flow_chart_data WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:17:58,775 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id, params, action_id FROM t_subtask WHERE 1=1 AND action_id = '' ; 
 
2025-09-08 09:17:58,781 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_test_result WHERE delete_flag = false AND type = 'RESULT_VAR' ; 
 
2025-09-08 09:17:58,784 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_result WHERE 1=1 AND delete_flag = 0 AND result_variable_id IN ('0d28f0e1-1393-41f3-9b13-be502bd04584') 
; 
 
2025-09-08 09:17:58,787 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_global_project_mapping WHERE 1=1 AND delete_flag = 0 AND project_id NOTNULL 
; 
 
2025-09-08 09:17:58,788 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT action_id, flow_chart_scheduler FROM t_action WHERE delete_flag = false ; 
 
2025-09-08 09:17:58,791 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id, params, action_id FROM t_subtask WHERE 1=1 AND action_id IN ('5a5f09ef-c362-437a-9a8a-8e1d86e455d1','679f70fa-d870-40d9-b121-c759b28044ed','aa25639b-ca7e-4f2e-9113-26cc42a12f6c','059424b8-2ff1-43c7-877a-1bda3abf54bf','271168ec-68e7-4807-bbb9-2d1d127feaa7','6ed1a4e6-a797-4b13-a81d-d00b2b141967','437289c3-f33c-4036-98a1-bb7cc6740fb4','858d991a-bade-4ef6-b394-36eb56e64780','334e466d-bdf9-495e-bd95-241aec10008f','6263aa5c-3aed-4e91-809e-706c226041c0','b18f9963-afaa-48a4-a5cb-73a029ffb8c0','0d84683a-47a6-47ea-bb0f-668955dcecdb','c41551f6-2fb2-433e-8d6d-4cac4793c249','bc4eff3c-d154-44ca-aa02-4ae24da241be','d278b617-ff31-41fe-84a6-3ae5e54149da','a048cdfc-ad4d-4108-b3bd-50da91663d64','9df7ca66-77f2-4809-872a-9efc68d048a4','66c287cb-50e4-4269-ae36-a91d4e1d0703','42fd8414-03db-4487-97b3-dedbd644fbda','caebc7dd-7b7b-49c0-9481-3c1f992ef2c1','d3784346-2780-4c48-8403-de57053fe23e','ac20609c-30be-473e-a9d3-bf39e1bd764a','d39e360f-acf5-4e3b-94d2-107cd5938491','fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f','ab56adb1-93c8-4ca0-a353-39b5c7a3ed16','867242e3-2471-4371-82fa-24db7253e028','35a50d1d-e1b9-4405-8f55-51b2d7e46789','e2c18c1e-62c7-427e-af54-3bd41ce5f8b8','a67517fc-f893-4b43-b7bf-2bdb16325bf4','4d8c8e73-c0e6-4500-952b-3e3cee815f39','ba1bec2b-af91-4e2c-8815-e685ff1c878b','e0449d34-2a68-4903-b755-0777eff578e8','01ae702e-513c-422a-822f-536178aaf22d','d2a28ac5-6be5-4a4c-806f-424fc39131ee','5f971f06-8061-402b-a27b-573a9a8a4b3b','40799e80-4904-461d-81c3-78d5292c0237','fbc2d565-2a68-473a-a2b5-7e932c5ae3a7','db4ae573-ed87-4f2c-b0a5-8c2f7e191983','facf71e7-e2ad-4e4c-9dc7-ee4c8c3e07cd','d387206f-efe9-4d8b-8020-38ece5121f0f','3601749f-b58b-4018-b8c3-e1bfc760f8e0','b8180c37-afe6-4b02-8fe6-5298a14b54f9','2e2782db-c485-4615-9808-4488b7007895','da399466-da97-449d-b998-0d7a0823cdd0','541ef62d-e173-4963-9a96-1b55f07523b7','6a579b71-e5fb-4bef-9569-33de699c61ea','a5112cfc-f9ee-4232-86e0-ff1e81e9df45','da633f41-5a7a-4b48-aefc-9c7e63a3e8a8','50753527-8b0e-419d-9ba2-c09916053748','0435e7a9-00cc-4721-81bf-af9843d16737','bb24c523-e5ac-4a81-8891-b28c75583058','a20de5f2-23db-42ea-bae7-7395a4742665','244c8013-2b7f-4254-af35-97709d2f3fca','4db1c6fe-8ae5-4b2d-ae0c-5fcc249d9b91','d6264fcd-fc87-4ba5-a8af-35236f156425','ec6e0760-ab6d-496e-bdd5-292be45ed606','e3ae9b9c-235f-4820-8de9-bbcfaa8a9efd','6ccb63fa-c223-4d35-861f-008c33cbbb9f','6b0632c6-80b1-4da2-88f8-81d49a6508a4','41c80162-da78-4d91-8a50-e19d59781b7e','e3c8ca6f-23bf-4fc6-8495-da6d17924867','c496d650-fe66-484c-b9c7-a901ff779cdb','63526397-7d5a-4d20-8840-4fa2ceee596d','b0dddc3a-5b8d-40fc-befd-37a4e03f9cf7','952ef7e6-11e2-4006-a1dd-ab5ac9fecd98','c20d06d7-fcb8-4a98-8cec-b559d461c8d6','523975db-051c-4497-9ad9-1634f1e5ff1d') 
; 
 
2025-09-08 09:17:58,807 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_flow_chart_data WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:17:58,811 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_input WHERE delete_flag = 0 AND (json_extract(program_tab, '$.isVisible') 
!= '' OR json_extract(program_tab, '$.isDisabled') != '' OR json_extract(program_tab, '$.mode') 
!= '' OR json_extract(program_tab, '$.isCheck') != '' OR json_extract(program_tab, '$.isCheckdisabledr') 
!= ''); 
 
2025-09-08 09:17:58,814 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_function WHERE delete_flag = false ; 
 
2025-09-08 09:17:58,815 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE delete_flag = false ; 
 
2025-09-08 09:17:58,818 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id, code FROM t_units WHERE 1=1 AND id IN ('e655c1f0-26c0-41aa-9d1d-25c94c059d2e','f7d1bcc0-cec4-4118-bb30-36b2995893d4','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','f9b2c0be-a829-4072-878e-f5f592308f79','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','02ad4c18-698f-4b25-935d-3f0501c2e1fb','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','','f9b2c0be-a829-4072-878e-f5f592308f79','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','fa93dbf6-960a-4191-a70a-6ae57061150b','fa93dbf6-960a-4191-a70a-6ae57061150b','f7d1bcc0-cec4-4118-bb30-36b2995893d4','02ad4c18-698f-4b25-935d-3f0501c2e1fb','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','85b70b7c-37cf-43b4-ba98-d27dc71efd1a','85b70b7c-37cf-43b4-ba98-d27dc71efd1a','85b70b7c-37cf-43b4-ba98-d27dc71efd1a','784b102e-a8a5-4943-9f6e-f720f29f0327','fecae037-0413-41dc-9276-f59ef1570121') 
; 
 
2025-09-08 09:17:58,820 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id, code FROM t_units_dimension WHERE 1=1 AND id IN ('6420a172-7a93-45c6-a8da-7ccb275a1aad','56019f36-d734-40c0-8872-73b60304f80a','6420a172-7a93-45c6-a8da-7ccb275a1aad','54891d75-4375-4253-8a5b-068423501a0a','6420a172-7a93-45c6-a8da-7ccb275a1aad','fed22294-bb3a-4cfe-b832-a5a49a4f80c7','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','','54891d75-4375-4253-8a5b-068423501a0a','6420a172-7a93-45c6-a8da-7ccb275a1aad','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','ebc759ee-b54c-489a-ac6d-0f8f892d15ae','ebc759ee-b54c-489a-ac6d-0f8f892d15ae','56019f36-d734-40c0-8872-73b60304f80a','fed22294-bb3a-4cfe-b832-a5a49a4f80c7','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','b757382f-2c6a-4f32-9630-413273c69050','b757382f-2c6a-4f32-9630-413273c69050','b757382f-2c6a-4f32-9630-413273c69050','7fa7a72d-541c-49b6-9aaf-cf2873abbf7e','3b533dda-6074-4ec2-8027-140b8c352072') 
; 
 
2025-09-08 09:17:58,821 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:17:58,828 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:17:58,829 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:03,243 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:18:03,244 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE 1 = 1 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' 
LIMIT 1; ; 
 
2025-09-08 09:18:03,276 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_page WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,277 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '1' AND binder_id IS 
NULL ; 
 
2025-09-08 09:18:03,278 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,279 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '2' AND binder_id IS 
NULL ; 
 
2025-09-08 09:18:03,280 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,281 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '3' AND binder_id IS 
NULL ; 
 
2025-09-08 09:18:03,282 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,283 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '1e46879c-298c-4491-bafe-2f04759775a9' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,284 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,285 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '0bc61403-d55a-4596-8be4-91157755902e' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,285 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,287 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '21cf43c1-0646-48ad-9f06-f04ef7396cdd' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,288 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,290 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '403b1882-f132-4b90-a1a0-0c22b0d0bd25' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,290 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,291 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '93532d6d-d611-4e79-933b-bc77a07c6d79' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,292 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,293 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = 'e224fbfb-d760-4fd5-b2ec-42b7c90a775f' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,293 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,351 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT id, widget_id, data FROM t_widget_data_source WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,380 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:18:03,382 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE 1=1 AND delete_flag = 0 AND sample_code = 'sample_14785d372' 
; 
 
2025-09-08 09:18:03,391 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT id, name, group_category, code, variable_type, default_val, number_tab, f1_index, is_enable, 
is_feature, is_fx, created_user_id, type, is_overall, contact_code, description, pic, related_result_variable_id, 
related_sub_task_id, default_val, number_tab, reasonable_val_tab, button_tab, button_variable_tab, 
program_tab, text_tab, boolean_tab, select_tab, two_digit_array_tab, control_tab, label_tab, 
picture_tab, related_var_tab, buffer_tab, custom_array_tab, double_array_tab, double_array_list_tab 
FROM t_variable_input WHERE 1=1 AND delete_flag = 0 AND name LIKE '%%' ORDER BY created_time 
DESC ; 
 
2025-09-08 09:18:03,669 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,670 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal_group WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,672 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 09:18:03,673 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND disable_flag 
= 0 AND parent_group != '0' AND id != '2' ; 
 
2025-09-08 09:18:03,675 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND parent_group != '0' ; 
 
2025-09-08 09:18:03,679 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group 
= '0' ; 
 
2025-09-08 09:18:03,681 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT id, name, color, code, status, sample_type, parent_group, disable_flag, created_user_id, 
data, samples FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,685 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,695 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_page WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,697 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT shortcut_id, shortcut_name, icon, show_type, dialog_id, order_num, shortcut_key, action_id, 
power, halving_line, created_user_id, program_tab, input_code, tip, gap_flag, need_login_check 
FROM t_shortcut WHERE delete_flag = false ORDER BY order_num ASC ; 
 
2025-09-08 09:18:03,697 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '1' AND binder_id IS 
NULL ; 
 
2025-09-08 09:18:03,698 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,699 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,701 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '2' AND binder_id IS 
NULL ; 
 
2025-09-08 09:18:03,702 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,705 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '3' AND binder_id IS 
NULL ; 
 
2025-09-08 09:18:03,705 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,705 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,711 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '1e46879c-298c-4491-bafe-2f04759775a9' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,714 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,717 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '0bc61403-d55a-4596-8be4-91157755902e' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,718 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,720 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '21cf43c1-0646-48ad-9f06-f04ef7396cdd' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,721 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,723 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '403b1882-f132-4b90-a1a0-0c22b0d0bd25' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,726 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,727 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '93532d6d-d611-4e79-933b-bc77a07c6d79' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,728 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,730 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = 'e224fbfb-d760-4fd5-b2ec-42b7c90a775f' 
AND binder_id IS NULL ; 
 
2025-09-08 09:18:03,731 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,756 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT trv.result_variable_id,trv.variable_name,trv.type,trv.code, trv.abbreviation,tu.name 
unit_name, trv.created_user_id,trv.signal_var_id,trv.display_modes,trv.format_type,trv.format_info,trv.dimension_id,trv.unit_id,trv.locked_unit_ids, 
trv.auxiliary_line_flag, trv.auxiliary_line_arr, trv.marking_flag, trv.marking_count, trv.marking_action, 
trv.description FROM t_variable_result trv LEFT JOIN t_units tu ON trv.unit_id = tu.id AND 
tu.delete_flag = false WHERE trv.delete_flag = false ; 
 
2025-09-08 09:18:03,758 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'cc34a725-fc60-4d44-8098-05319e06cab7' 
AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,758 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_guide_dialog WHERE 1=1 AND delete_flag = 0 AND type = 'general' AND dialog_name 
LIKE '%%' ; 
 
2025-09-08 09:18:03,759 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'aa35ee11-dd20-4385-b76a-9d5edd8a8c0f' 
AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,760 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'a60e09cc-f711-4042-a1e9-29511b11b414' 
AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,761 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = '3a28d0eb-423b-4dfb-8e7c-c36ff1cdfc35' 
AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,761 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = '0b90ea68-ac19-4202-8ab8-411492f6a207' 
AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,762 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'd9f20c00-926e-4a0e-b122-642c25aa02b1' 
AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,763 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'fcd4dbf2-7654-4474-a678-4d79eb25864c' 
AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,764 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'b6b16edb-801f-4195-b0cc-87f158abc572' 
AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,765 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = '51a798fc-195e-402c-88b8-41b055fdd404' 
AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,783 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_test_result WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,803 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT id,name,code,default_unit_id,created_user_id,delete_flag FROM t_units_dimension WHERE 
delete_flag = false ORDER BY created_time; 
 
2025-09-08 09:18:03,804 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT tu.id,tu.name,tu.code,tu.dimension_id,tu.created_user_id, tu.order_num,tu.visible,tu.proportion,tu.standard_id,tu.delete_flag, 
tus.name standard,tud.name dimension FROM t_units tu LEFT JOIN t_units_standard tus ON tu.standard_id 
= tus.id LEFT JOIN t_units_dimension tud ON tu.dimension_id = tud.id WHERE tu.delete_flag = 
false ; 
 
2025-09-08 09:18:03,835 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT id, variable_code, general_tab, in_process_tab, not_in_process_tab FROM t_passage_form 
WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,870 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_guide WHERE delete_flag = false AND type = 'general' ; 
 
2025-09-08 09:18:03,884 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT video_id, video_name, video_type, remark, name_file, sample_code, video_file, current_create_time, 
sample_id FROM t_video WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,914 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:18:03,942 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT * FROM t_table_config WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:03,953 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_export_config WHERE delete_flag = false ; 
 
2025-09-08 09:18:03,985 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_picture WHERE 1 = 1 AND id = '85b303d7-5008-4bec-9429-f75a971bcca3' LIMIT 1; 
; 
 
2025-09-08 09:18:04,011 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT id, name, code, func FROM t_sample_instance_about WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:04,041 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 09:18:04,083 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_double_array_curve WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,110 [qtp1566353334-139] INFO  jdbc.sqlonly - SELECT * FROM t_static_curve WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,139 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_auxiliary_line WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,172 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_dynamic_curve WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:04,184 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('b86ab2de-fe84-40b3-945e-2c9facecb2b5','e73efa10-e353-4a08-9ad2-a3a9978c74b4','19939b17-ffa1-4e49-bf6f-7b08487f883a','982aa300-613c-4dfe-a3d9-598da8a20c3d','e8f2731b-1d04-4ba1-97ed-15d8bc05ab07','21ff5e20-3a2e-4deb-b8ea-b87c2267f927') 
; 
 
2025-09-08 09:18:04,185 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '19939b17-ffa1-4e49-bf6f-7b08487f883a' 
; 
 
2025-09-08 09:18:04,186 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,187 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '21ff5e20-3a2e-4deb-b8ea-b87c2267f927' 
; 
 
2025-09-08 09:18:04,188 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,189 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '982aa300-613c-4dfe-a3d9-598da8a20c3d' 
; 
 
2025-09-08 09:18:04,189 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,190 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'b86ab2de-fe84-40b3-945e-2c9facecb2b5' 
; 
 
2025-09-08 09:18:04,191 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,192 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'e73efa10-e353-4a08-9ad2-a3a9978c74b4' 
; 
 
2025-09-08 09:18:04,193 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,194 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'e8f2731b-1d04-4ba1-97ed-15d8bc05ab07' 
; 
 
2025-09-08 09:18:04,194 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,209 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 AND id = 1 LIMIT 1; ; 
 
2025-09-08 09:18:04,238 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 09:18:04,240 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND disable_flag 
= 0 AND parent_group != '0' AND id != '2' ; 
 
2025-09-08 09:18:04,241 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND parent_group != '0' ; 
 
2025-09-08 09:18:04,243 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group 
= '0' ; 
 
2025-09-08 09:18:04,244 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT id, name, color, code, status, sample_type, parent_group, disable_flag, created_user_id, 
data, samples FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:04,289 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT sample_id,sample_name,img,code,created_user_id FROM t_sample WHERE delete_flag = false; 
 
2025-09-08 09:18:04,290 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT tsp.parameter_id,tsp.parameter_name,tsp.code,tsp.delete_flag,tsp.created_user_id, tsp.sample_id,tsp.order_num,tsp.dimension_id,tsp.units_id,tsp.default_val,tsp.parameter_img, 
tsp.func,tsp.abbreviation,tsp.hidden_flag, tsp.data_type,tsp.select_options,tsp.is_visible_func,tsp.is_disabled_func 
FROM t_sample_parameter tsp WHERE delete_flag = false; 
 
2025-09-08 09:18:04,315 [qtp1566353334-165] INFO  jdbc.sqlonly - UPDATE t_select_sample SET sample_code = 'sample_14785d372' WHERE id = 1 ; 
 
2025-09-08 09:18:04,420 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('51390fb6-23ca-4e39-9cc0-38d56c3886a8','54cbdb4f-4b80-4248-a25d-c45d9917bf18','82edce6a-3090-43fd-b6fe-e53086b8dc0d','a2abafd9-d6e1-4342-985c-9dbfc9ed0b6b') 
; 
 
2025-09-08 09:18:04,420 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '51390fb6-23ca-4e39-9cc0-38d56c3886a8' 
; 
 
2025-09-08 09:18:04,421 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,422 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '54cbdb4f-4b80-4248-a25d-c45d9917bf18' 
; 
 
2025-09-08 09:18:04,423 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,424 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '82edce6a-3090-43fd-b6fe-e53086b8dc0d' 
; 
 
2025-09-08 09:18:04,425 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,426 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a2abafd9-d6e1-4342-985c-9dbfc9ed0b6b' 
; 
 
2025-09-08 09:18:04,426 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,447 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:18:04,449 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE 1=1 AND delete_flag = 0 AND sample_code = 'sample_14785d372' 
; 
 
2025-09-08 09:18:04,456 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT id, name, group_category, code, variable_type, default_val, number_tab, f1_index, is_enable, 
is_feature, is_fx, created_user_id, type, is_overall, contact_code, description, pic, related_result_variable_id, 
related_sub_task_id, default_val, number_tab, reasonable_val_tab, button_tab, button_variable_tab, 
program_tab, text_tab, boolean_tab, select_tab, two_digit_array_tab, control_tab, label_tab, 
picture_tab, related_var_tab, buffer_tab, custom_array_tab, double_array_tab, double_array_list_tab 
FROM t_variable_input WHERE 1=1 AND delete_flag = 0 AND name LIKE '%%' ORDER BY created_time 
DESC ; 
 
2025-09-08 09:18:04,732 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('44d5f88b-bd4b-47db-b7fa-cdb029e5cce5','8ac30228-c044-4ed4-8e0e-5f6c249f4229','371d8fe1-292b-4f57-859a-af1f4e5b95c2','a65e2f91-e791-4d41-b5d8-996a160c6f11') 
; 
 
2025-09-08 09:18:04,734 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '371d8fe1-292b-4f57-859a-af1f4e5b95c2' 
; 
 
2025-09-08 09:18:04,734 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('ff165ef5-aed8-4e76-8d94-2cc1a1ad46bf','1a9fa552-d195-4afd-aa57-d387152d9d3c','f39791e4-c907-4ad7-a5d5-2a01995e53c7','5d38b5ce-9302-460f-884b-7e99ef223f8e') 
; 
 
2025-09-08 09:18:04,735 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,736 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '1a9fa552-d195-4afd-aa57-d387152d9d3c' 
; 
 
2025-09-08 09:18:04,736 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('a7be1fc6-0031-4ba0-8e61-3a5509c68f71','2dc40297-0dda-41bb-bfed-5d891fa4ad6e','9430a45d-fd05-4976-bb04-7708f9532661','6060c033-c826-4d7f-9a38-387e6ed6050f') 
; 
 
2025-09-08 09:18:04,736 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,737 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '2dc40297-0dda-41bb-bfed-5d891fa4ad6e' 
; 
 
2025-09-08 09:18:04,737 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('8429ab78-098e-45d3-8d0c-3d40ef6dc8be','bde875fc-cea9-425a-8df2-57f7e9754387') 
; 
 
2025-09-08 09:18:04,738 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('05efd2bd-1fc9-4f07-884e-9e27f838d797','6e103de6-e8b8-4cd4-8d68-8a6ed8edd63a') 
; 
 
2025-09-08 09:18:04,739 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '8429ab78-098e-45d3-8d0c-3d40ef6dc8be' 
; 
 
2025-09-08 09:18:04,739 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,740 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '05efd2bd-1fc9-4f07-884e-9e27f838d797' 
; 
 
2025-09-08 09:18:04,741 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,741 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,744 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '44d5f88b-bd4b-47db-b7fa-cdb029e5cce5' 
; 
 
2025-09-08 09:18:04,745 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,748 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '5d38b5ce-9302-460f-884b-7e99ef223f8e' 
; 
 
2025-09-08 09:18:04,749 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,751 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '6060c033-c826-4d7f-9a38-387e6ed6050f' 
; 
 
2025-09-08 09:18:04,753 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,753 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'bde875fc-cea9-425a-8df2-57f7e9754387' 
; 
 
2025-09-08 09:18:04,755 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '6e103de6-e8b8-4cd4-8d68-8a6ed8edd63a' 
; 
 
2025-09-08 09:18:04,757 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '8ac30228-c044-4ed4-8e0e-5f6c249f4229' 
; 
 
2025-09-08 09:18:04,757 [qtp1566353334-163] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,757 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,759 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,760 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '9430a45d-fd05-4976-bb04-7708f9532661' 
; 
 
2025-09-08 09:18:04,760 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'f39791e4-c907-4ad7-a5d5-2a01995e53c7' 
; 
 
2025-09-08 09:18:04,760 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,762 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,771 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a7be1fc6-0031-4ba0-8e61-3a5509c68f71' 
; 
 
2025-09-08 09:18:04,771 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a65e2f91-e791-4d41-b5d8-996a160c6f11' 
; 
 
2025-09-08 09:18:04,771 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'ff165ef5-aed8-4e76-8d94-2cc1a1ad46bf' 
; 
 
2025-09-08 09:18:04,773 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,773 [qtp1566353334-167] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,773 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:18:04,790 [qtp1566353334-175] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 09:18:04,791 [qtp1566353334-175] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE 1=1 AND delete_flag = 0 AND sample_code = 'sample_14785d372' 
; 
 
2025-09-08 09:18:04,795 [qtp1566353334-175] INFO  jdbc.sqlonly - SELECT id, name, group_category, code, variable_type, default_val, number_tab, f1_index, is_enable, 
is_feature, is_fx, created_user_id, type, is_overall, contact_code, description, pic, related_result_variable_id, 
related_sub_task_id, default_val, number_tab, reasonable_val_tab, button_tab, button_variable_tab, 
program_tab, text_tab, boolean_tab, select_tab, two_digit_array_tab, control_tab, label_tab, 
picture_tab, related_var_tab, buffer_tab, custom_array_tab, double_array_tab, double_array_list_tab 
FROM t_variable_input WHERE 1=1 AND delete_flag = 0 AND id IN ('2') ORDER BY created_time DESC 
; 
 
2025-09-08 09:18:11,081 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= '6a579b71-e5fb-4bef-9569-33de699c61ea' ; 
 
2025-09-08 09:18:11,312 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = '6a579b71-e5fb-4bef-9569-33de699c61ea' LIMIT 
1; ; 
 
2025-09-08 09:18:11,313 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = '6a579b71-e5fb-4bef-9569-33de699c61ea' 
; 
 
2025-09-08 09:18:11,316 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:11,318 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【打开项目默认执行动作】') 
; 
 
2025-09-08 09:18:11,623 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwcxksbs' and delete_flag = false; 
 
2025-09-08 09:18:11,625 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwcxksbs' and delete_flag 
= false; 
 
2025-09-08 09:18:11,629 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwbs' and delete_flag = false; 
 
2025-09-08 09:18:11,630 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":false,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwbs' and delete_flag = 
false; 
 
2025-09-08 09:18:11,674 [async-dispatch-13] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:11,676 [async-dispatch-13] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【打开项目默认执行动作】') 
; 
 
2025-09-08 09:18:15,182 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= '5a5f09ef-c362-437a-9a8a-8e1d86e455d1' ; 
 
2025-09-08 09:18:15,201 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = '5a5f09ef-c362-437a-9a8a-8e1d86e455d1' LIMIT 
1; ; 
 
2025-09-08 09:18:15,202 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = '5a5f09ef-c362-437a-9a8a-8e1d86e455d1' 
; 
 
2025-09-08 09:18:15,204 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:15,205 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【联机】') 
; 
 
2025-09-08 09:18:15,332 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:18,844 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'GLOBAL' and variable_code = 'input_ifopendivice' 
and delete_flag = false; 
 
2025-09-08 09:18:18,867 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"unit":null,"unitType":null,"type":null}' 
WHERE 1=1 and variable_code = 'input_ifopendivice' ; 
 
2025-09-08 09:18:19,058 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'GLOBAL' and variable_code = 'input_ifstationon' 
and delete_flag = false; 
 
2025-09-08 09:18:19,061 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"unit":null,"unitType":null,"type":null}' 
WHERE 1=1 and variable_code = 'input_ifstationon' ; 
 
2025-09-08 09:18:19,558 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'GLOBAL' and variable_code = 'input_ifstationon' 
and delete_flag = false; 
 
2025-09-08 09:18:19,560 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":false,"unit":null,"unitType":null,"type":null}' 
WHERE 1=1 and variable_code = 'input_ifstationon' ; 
 
2025-09-08 09:18:25,007 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= '679f70fa-d870-40d9-b121-c759b28044ed' ; 
 
2025-09-08 09:18:25,033 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = '679f70fa-d870-40d9-b121-c759b28044ed' LIMIT 
1; ; 
 
2025-09-08 09:18:25,034 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = '679f70fa-d870-40d9-b121-c759b28044ed' 
; 
 
2025-09-08 09:18:25,036 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:25,037 [qtp1566353334-164] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【启动】') 
; 
 
2025-09-08 09:18:25,133 [async-dispatch-11] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:25,135 [async-dispatch-11] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【启动】') 
; 
 
2025-09-08 09:18:25,135 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:25,578 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'GLOBAL' and variable_code = 'input_ifstationon' 
and delete_flag = false; 
 
2025-09-08 09:18:25,580 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"unit":null,"unitType":null,"type":null}' 
WHERE 1=1 and variable_code = 'input_ifstationon' ; 
 
2025-09-08 09:18:26,652 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= 'fbc2d565-2a68-473a-a2b5-7e932c5ae3a7' ; 
 
2025-09-08 09:18:26,681 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'fbc2d565-2a68-473a-a2b5-7e932c5ae3a7' LIMIT 
1; ; 
 
2025-09-08 09:18:26,682 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'fbc2d565-2a68-473a-a2b5-7e932c5ae3a7' 
; 
 
2025-09-08 09:18:26,685 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:26,687 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【终止预制裂纹】') 
; 
 
2025-09-08 09:18:26,790 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - INSERT INTO t_log (level,content) VALUES ( 'error', '流程图未开始或已结束') ; 
 
2025-09-08 09:18:26,797 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:27,789 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= 'ab56adb1-93c8-4ca0-a353-39b5c7a3ed16' ; 
 
2025-09-08 09:18:27,811 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'ab56adb1-93c8-4ca0-a353-39b5c7a3ed16' LIMIT 
1; ; 
 
2025-09-08 09:18:27,814 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'ab56adb1-93c8-4ca0-a353-39b5c7a3ed16' 
; 
 
2025-09-08 09:18:27,826 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:27,827 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【预制裂纹流程图】') 
; 
 
2025-09-08 09:18:27,860 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwcxksbs' and delete_flag = false; 
 
2025-09-08 09:18:27,862 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwcxksbs' and delete_flag 
= false; 
 
2025-09-08 09:18:27,865 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwhcq' and delete_flag = false; 
 
2025-09-08 09:18:27,867 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":"RESTART","isConstant":0,"id":1744118983145,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwhcq' and delete_flag 
= false; 
 
2025-09-08 09:18:27,872 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwbs' and delete_flag = false; 
 
2025-09-08 09:18:27,873 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":false,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwbs' and delete_flag = 
false; 
 
2025-09-08 09:18:38,242 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:38,537 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwcsts' and delete_flag = false; 
 
2025-09-08 09:18:38,540 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":"\u8bf7\u786e\u8ba4\u662f\u5426\u4f7f\u7528\u8bd5\u6837147\u8fdb\u884c\u5e73\u9762\u5e94\u53d8\u65ad\u88c2\u97e7\u5ea6\u8bd5\u9a8c 
\u9884\u5236\u75b2\u52b3\u88c2\u7eb9\u8bd5\u9a8c\uff1f","isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwcsts' and delete_flag 
= false; 
 
2025-09-08 09:18:38,646 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwcxksbs' and delete_flag = false; 
 
2025-09-08 09:18:38,649 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwcxksbs' and delete_flag 
= false; 
 
2025-09-08 09:18:38,658 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwhcq' and delete_flag = false; 
 
2025-09-08 09:18:38,661 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":"RESTART","isConstant":0,"id":1744118983145,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwhcq' and delete_flag 
= false; 
 
2025-09-08 09:18:38,666 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwbs' and delete_flag = false; 
 
2025-09-08 09:18:38,668 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":false,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwbs' and delete_flag = 
false; 
 
2025-09-08 09:18:38,672 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_fzx1' and delete_flag = false; 
 
2025-09-08 09:18:38,675 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":0,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_fzx1' and delete_flag 
= false; 
 
2025-09-08 09:18:38,685 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_fzx2' and delete_flag = false; 
 
2025-09-08 09:18:38,690 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":0,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_fzx2' and delete_flag 
= false; 
 
2025-09-08 09:18:38,700 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_fzx_qd' and delete_flag = false; 
 
2025-09-08 09:18:38,704 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":0,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_fzx_qd' and delete_flag 
= false; 
 
2025-09-08 09:18:38,713 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_fzx_zd' and delete_flag = false; 
 
2025-09-08 09:18:38,718 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":0,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_fzx_zd' and delete_flag 
= false; 
 
2025-09-08 09:18:38,731 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwcsts' and delete_flag = false; 
 
2025-09-08 09:18:38,739 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":"\u8bf7\u786e\u8ba4\u662f\u5426\u4f7f\u7528\u8bd5\u6837147\u8fdb\u884c\u5e73\u9762\u5e94\u53d8\u65ad\u88c2\u97e7\u5ea6\u8bd5\u9a8c 
\u9884\u5236\u75b2\u52b3\u88c2\u7eb9\u8bd5\u9a8c\uff1f","isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwcsts' and delete_flag 
= false; 
 
2025-09-08 09:18:38,781 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'a5112cfc-f9ee-4232-86e0-ff1e81e9df45' LIMIT 
1; ; 
 
2025-09-08 09:18:38,782 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'a5112cfc-f9ee-4232-86e0-ff1e81e9df45' 
; 
 
2025-09-08 09:18:38,785 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:38,790 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【关闭项目时默认执行动作】') 
; 
 
2025-09-08 09:18:39,131 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1=1 AND delete_flag = 0 AND project_name IN () AND project_id 
NOT IN (31) ; 
 
2025-09-08 09:18:39,131 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:39,132 [async-dispatch-16] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(PASSIVE) 
 
2025-09-08 09:18:39,139 [async-dispatch-2] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:39,142 [async-dispatch-2] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【关闭项目时默认执行动作】') 
; 
 
2025-09-08 09:18:39,231 [async-dispatch-9] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:39,235 [async-dispatch-9] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【终止预制裂纹】') 
; 
 
2025-09-08 09:18:39,253 [async-dispatch-16] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(RESTART) 
 
2025-09-08 09:18:39,253 [async-dispatch-16] INFO  jdbc.sqlonly - PRAGMA optimize 
 
2025-09-08 09:18:39,255 [async-dispatch-16] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(PASSIVE) 
 
2025-09-08 09:18:39,362 [async-dispatch-16] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(RESTART) 
 
2025-09-08 09:18:39,362 [async-dispatch-16] INFO  jdbc.sqlonly - PRAGMA optimize 
 
2025-09-08 09:18:39,367 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:18:39,370 [async-dispatch-16] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '保存了项目') ; 
 
2025-09-08 09:18:39,372 [async-dispatch-16] INFO  jdbc.sqlonly - UPDATE t_project_db SET updated_time = '2025-09-08 09:18:39' WHERE project_id = 31 ; 
 
2025-09-08 09:18:39,375 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT * FROM t_variable_input WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:18:39,407 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT * FROM t_variable_result WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:18:39,409 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:18:39,411 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT * FROM t_sample WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:18:39,413 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT * FROM t_sample_parameter WHERE delete_flag = false AND sample_id = 'ef1539f0-cde5-4644-b8bd-12268ce19793' 
ORDER BY created_time ASC ; 
 
2025-09-08 09:18:39,414 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT * FROM t_sample_parameter WHERE delete_flag = false AND sample_id = 'faaaa94a-6f84-4ecb-ba47-cac5c2511b71' 
ORDER BY created_time ASC ; 
 
2025-09-08 09:18:39,415 [async-dispatch-16] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:18:39,431 [async-dispatch-16] INFO  jdbc.sqlonly - INSERT INTO t_template_comparison_signature (type, signature) VALUES ('input','9fbc4ac03c9d8da7773d56214fe5b966d3dbc97f33f266aad0460cd3999e2ee3'),('result','64fc7d10d3d9100b6ca30bb38e8b62c96851893a372e63e5eed464c3aecdf52c'),('signal','1c9a06f8de41109c9109697b26b79edbf2414f3d1f11234acd649c7645c758ff'),('sample-type','fabfe042f5b2060ccd3a147f8521b8d15d7ca686d4ed70b4ff61785a594aa5ad'),('action','f3a75c58f1da9ba29b83b5ea4505ca8ee1368f5b4ec9f6cfe85c29539108652f') 
ON CONFLICT(type) DO UPDATE SET `type` = excluded.type,`signature` = excluded.signature ; 
 
2025-09-08 09:18:42,517 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_three' and delete_flag = false; 
 
2025-09-08 09:18:42,521 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"value_type":"string","unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_three' and delete_flag = 
false; 
 
2025-09-08 09:18:42,699 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwbs' and delete_flag = false; 
 
2025-09-08 09:18:42,701 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwbs' and delete_flag = 
false; 
 
2025-09-08 09:18:42,820 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT id,name,code,default_unit_id,created_user_id,delete_flag FROM t_units_dimension WHERE 
delete_flag = false ORDER BY created_time; 
 
2025-09-08 09:18:42,820 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT tu.id,tu.name,tu.code,tu.dimension_id,tu.created_user_id, tu.order_num,tu.visible,tu.proportion,tu.standard_id,tu.delete_flag, 
tus.name standard,tud.name dimension FROM t_units tu LEFT JOIN t_units_standard tus ON tu.standard_id 
= tus.id LEFT JOIN t_units_dimension tud ON tu.dimension_id = tud.id WHERE tu.delete_flag = 
false ; 
 
2025-09-08 09:18:42,928 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_dqzszqs' and delete_flag = false; 
 
2025-09-08 09:18:42,930 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":0,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_dqzszqs' and delete_flag 
= false; 
 
2025-09-08 09:18:42,933 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units WHERE 1 = 1 AND delete_flag = 0 AND code = 'unit_cdw178e4a3' LIMIT 1; 
; 
 
2025-09-08 09:18:42,934 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units_dimension WHERE 1 = 1 AND delete_flag = 0 AND code = 'dimension_cycle' 
LIMIT 1; ; 
 
2025-09-08 09:18:42,934 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_starting_cycle' and delete_flag = false; 
 
2025-09-08 09:18:42,936 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":"fecae037-0413-41dc-9276-f59ef1570121","value":0,"type":null,"isConstant":0,"value_type":"string","unitType":"3b533dda-6074-4ec2-8027-140b8c352072"}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_starting_cycle' and delete_flag 
= false; 
 
2025-09-08 09:18:43,186 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 09:18:43,186 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND disable_flag 
= 0 AND parent_group != '0' AND id != '2' ; 
 
2025-09-08 09:18:43,187 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND parent_group != '0' ; 
 
2025-09-08 09:18:43,188 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group 
= '0' ; 
 
2025-09-08 09:18:43,189 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT id, name, color, code, status, sample_type, parent_group, disable_flag, created_user_id, 
data, samples FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:18:43,806 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_valley' and delete_flag = false; 
 
2025-09-08 09:18:43,808 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":1.6896318324019572,"isConstant":0,"value_type":"string","unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_valley' and delete_flag 
= false; 
 
2025-09-08 09:18:43,810 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_peak' and delete_flag = false; 
 
2025-09-08 09:18:43,812 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":16.89631832401957,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_peak' and delete_flag 
= false; 
 
2025-09-08 09:18:43,815 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwxhcs' and delete_flag = false; 
 
2025-09-08 09:18:43,817 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":10000000,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwxhcs' and delete_flag 
= false; 
 
2025-09-08 09:18:44,076 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_one' and delete_flag = false; 
 
2025-09-08 09:18:44,079 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"value_type":"string","unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_one' and delete_flag = false; 
 
2025-09-08 09:18:44,847 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 AND id = 1 LIMIT 1; ; 
 
2025-09-08 09:18:45,997 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_allKmax' and delete_flag = false; 
 
2025-09-08 09:18:45,999 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":850.8265709574918,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_allKmax' and delete_flag 
= false; 
 
2025-09-08 09:20:59,960 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= 'fbc2d565-2a68-473a-a2b5-7e932c5ae3a7' ; 
 
2025-09-08 09:20:59,985 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'fbc2d565-2a68-473a-a2b5-7e932c5ae3a7' LIMIT 
1; ; 
 
2025-09-08 09:20:59,989 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'fbc2d565-2a68-473a-a2b5-7e932c5ae3a7' 
; 
 
2025-09-08 09:20:59,991 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:20:59,992 [qtp1566353334-166] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【终止预制裂纹】') 
; 
 
2025-09-08 09:21:00,101 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:21:00,280 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:00,281 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【预制裂纹流程图】') 
; 
 
2025-09-08 09:21:00,429 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_dqzszqs' and delete_flag = false; 
 
2025-09-08 09:21:00,432 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":1889,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_dqzszqs' and delete_flag 
= false; 
 
2025-09-08 09:21:00,439 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units WHERE 1 = 1 AND delete_flag = 0 AND code = 'unit_cdw178e4a3' LIMIT 1; 
; 
 
2025-09-08 09:21:00,439 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units_dimension WHERE 1 = 1 AND delete_flag = 0 AND code = 'dimension_cycle' 
LIMIT 1; ; 
 
2025-09-08 09:21:00,439 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_starting_cycle' and delete_flag = false; 
 
2025-09-08 09:21:00,442 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":"fecae037-0413-41dc-9276-f59ef1570121","value":1889,"type":null,"isConstant":0,"value_type":"string","unitType":"3b533dda-6074-4ec2-8027-140b8c352072"}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_starting_cycle' and delete_flag 
= false; 
 
2025-09-08 09:21:01,422 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwcxksbs' and delete_flag = false; 
 
2025-09-08 09:21:01,423 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwcxksbs' and delete_flag 
= false; 
 
2025-09-08 09:21:01,426 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwhcq' and delete_flag = false; 
 
2025-09-08 09:21:01,427 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":"RESTART","isConstant":0,"id":1744118983145,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwhcq' and delete_flag 
= false; 
 
2025-09-08 09:21:01,431 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwbs' and delete_flag = false; 
 
2025-09-08 09:21:01,433 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":false,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwbs' and delete_flag = 
false; 
 
2025-09-08 09:21:01,436 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_kf' and delete_flag = false; 
 
2025-09-08 09:21:01,438 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":0,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_kf' and delete_flag = false; 
 
2025-09-08 09:21:01,441 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_allKmax' and delete_flag = false; 
 
2025-09-08 09:21:01,444 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":850.8265709574918,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_allKmax' and delete_flag 
= false; 
 
2025-09-08 09:21:01,448 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_kf' and delete_flag = false; 
 
2025-09-08 09:21:01,450 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":0,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_kf' and delete_flag = false; 
 
2025-09-08 09:21:01,454 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_zzzdylqdyz' and delete_flag = false; 
 
2025-09-08 09:21:01,458 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":850.8265709574918,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_zzzdylqdyz' and delete_flag 
= false; 
 
2025-09-08 09:21:01,466 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_zzzdl' and delete_flag = false; 
 
2025-09-08 09:21:01,469 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":16.896318435668945,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_zzzdl' and delete_flag = 
false; 
 
2025-09-08 09:21:01,477 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwjglwcd' and delete_flag = false; 
 
2025-09-08 09:21:01,480 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":47.10634592795962,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwjglwcd' and delete_flag 
= false; 
 
2025-09-08 09:21:01,540 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwcxksbs' and delete_flag = false; 
 
2025-09-08 09:21:01,541 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwcxksbs' and delete_flag 
= false; 
 
2025-09-08 09:21:01,545 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwhcq' and delete_flag = false; 
 
2025-09-08 09:21:01,546 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":"RESTART","isConstant":0,"id":1744118983145,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwhcq' and delete_flag 
= false; 
 
2025-09-08 09:21:01,551 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwbs' and delete_flag = false; 
 
2025-09-08 09:21:01,553 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":false,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwbs' and delete_flag = 
false; 
 
2025-09-08 09:21:01,556 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_fzx1' and delete_flag = false; 
 
2025-09-08 09:21:01,559 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":13.85498113632202,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_fzx1' and delete_flag 
= false; 
 
2025-09-08 09:21:01,562 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_fzx2' and delete_flag = false; 
 
2025-09-08 09:21:01,563 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":4.73096923828125,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_fzx2' and delete_flag 
= false; 
 
2025-09-08 09:21:01,567 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_fzx_qd' and delete_flag = false; 
 
2025-09-08 09:21:01,569 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":2.689631938934326,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_fzx_qd' and delete_flag 
= false; 
 
2025-09-08 09:21:01,573 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_fzx_zd' and delete_flag = false; 
 
2025-09-08 09:21:01,575 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":17.896318435668945,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_fzx_zd' and delete_flag 
= false; 
 
2025-09-08 09:21:01,588 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'a5112cfc-f9ee-4232-86e0-ff1e81e9df45' LIMIT 
1; ; 
 
2025-09-08 09:21:01,590 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'a5112cfc-f9ee-4232-86e0-ff1e81e9df45' 
; 
 
2025-09-08 09:21:01,593 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:01,595 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【关闭项目时默认执行动作】') 
; 
 
2025-09-08 09:21:01,707 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1=1 AND delete_flag = 0 AND project_name IN () AND project_id 
NOT IN (31) ; 
 
2025-09-08 09:21:01,708 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:01,708 [async-dispatch-19] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(PASSIVE) 
 
2025-09-08 09:21:01,715 [async-dispatch-8] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:01,717 [async-dispatch-8] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【关闭项目时默认执行动作】') 
; 
 
2025-09-08 09:21:01,814 [async-dispatch-19] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(RESTART) 
 
2025-09-08 09:21:01,814 [async-dispatch-19] INFO  jdbc.sqlonly - PRAGMA optimize 
 
2025-09-08 09:21:01,816 [async-dispatch-19] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(PASSIVE) 
 
2025-09-08 09:21:01,849 [async-dispatch-12] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:01,850 [async-dispatch-12] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【终止预制裂纹】') 
; 
 
2025-09-08 09:21:01,922 [async-dispatch-19] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(RESTART) 
 
2025-09-08 09:21:01,924 [async-dispatch-19] INFO  jdbc.sqlonly - PRAGMA optimize 
 
2025-09-08 09:21:01,925 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:01,927 [async-dispatch-19] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '保存了项目') ; 
 
2025-09-08 09:21:01,928 [async-dispatch-19] INFO  jdbc.sqlonly - UPDATE t_project_db SET updated_time = '2025-09-08 09:21:01' WHERE project_id = 31 ; 
 
2025-09-08 09:21:01,929 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT * FROM t_variable_input WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:21:01,957 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT * FROM t_variable_result WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:21:01,958 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:21:01,960 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT * FROM t_sample WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:21:01,961 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT * FROM t_sample_parameter WHERE delete_flag = false AND sample_id = 'ef1539f0-cde5-4644-b8bd-12268ce19793' 
ORDER BY created_time ASC ; 
 
2025-09-08 09:21:01,962 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT * FROM t_sample_parameter WHERE delete_flag = false AND sample_id = 'faaaa94a-6f84-4ecb-ba47-cac5c2511b71' 
ORDER BY created_time ASC ; 
 
2025-09-08 09:21:01,963 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 09:21:01,968 [async-dispatch-19] INFO  jdbc.sqlonly - INSERT INTO t_template_comparison_signature (type, signature) VALUES ('input','9fbc4ac03c9d8da7773d56214fe5b966d3dbc97f33f266aad0460cd3999e2ee3'),('result','64fc7d10d3d9100b6ca30bb38e8b62c96851893a372e63e5eed464c3aecdf52c'),('signal','1c9a06f8de41109c9109697b26b79edbf2414f3d1f11234acd649c7645c758ff'),('sample-type','fabfe042f5b2060ccd3a147f8521b8d15d7ca686d4ed70b4ff61785a594aa5ad'),('action','f3a75c58f1da9ba29b83b5ea4505ca8ee1368f5b4ec9f6cfe85c29539108652f') 
ON CONFLICT(type) DO UPDATE SET `type` = excluded.type,`signature` = excluded.signature ; 
 
2025-09-08 09:21:16,407 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= 'd2a28ac5-6be5-4a4c-806f-424fc39131ee' ; 
 
2025-09-08 09:21:16,431 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'd2a28ac5-6be5-4a4c-806f-424fc39131ee' LIMIT 
1; ; 
 
2025-09-08 09:21:16,433 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'd2a28ac5-6be5-4a4c-806f-424fc39131ee' 
; 
 
2025-09-08 09:21:16,439 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:16,440 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【裂纹检查新版流程图】') 
; 
 
2025-09-08 09:21:16,803 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 09:21:16,804 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND disable_flag 
= 0 AND parent_group != '0' AND id != '2' ; 
 
2025-09-08 09:21:16,805 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND parent_group != '0' ; 
 
2025-09-08 09:21:16,806 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group 
= '0' ; 
 
2025-09-08 09:21:16,807 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT id, name, color, code, status, sample_type, parent_group, disable_flag, created_user_id, 
data, samples FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:21:16,979 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:16,983 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:17,062 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:17,064 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:17,078 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:17,079 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:17,091 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:17,093 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:17,106 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:17,108 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:17,124 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:17,127 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:19,208 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 AND id = 1 LIMIT 1; ; 
 
2025-09-08 09:21:36,844 [qtp1566353334-140] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= 'b8180c37-afe6-4b02-8fe6-5298a14b54f9' ; 
 
2025-09-08 09:21:36,871 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'b8180c37-afe6-4b02-8fe6-5298a14b54f9' LIMIT 
1; ; 
 
2025-09-08 09:21:36,872 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'b8180c37-afe6-4b02-8fe6-5298a14b54f9' 
; 
 
2025-09-08 09:21:36,875 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:36,877 [qtp1566353334-165] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【终止裂纹长度检查】') 
; 
 
2025-09-08 09:21:36,938 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:36,940 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【裂纹检查新版流程图】') 
; 
 
2025-09-08 09:21:37,013 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'da399466-da97-449d-b998-0d7a0823cdd0' LIMIT 
1; ; 
 
2025-09-08 09:21:37,013 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'da399466-da97-449d-b998-0d7a0823cdd0' 
; 
 
2025-09-08 09:21:37,015 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:37,016 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【横梁停止】') 
; 
 
2025-09-08 09:21:37,439 [async-dispatch-19] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:37,441 [async-dispatch-19] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【横梁停止】') 
; 
 
2025-09-08 09:21:37,622 [async-dispatch-1] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:37,623 [async-dispatch-1] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【终止裂纹长度检查】') 
; 
 
2025-09-08 09:21:39,710 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= 'd2a28ac5-6be5-4a4c-806f-424fc39131ee' ; 
 
2025-09-08 09:21:39,990 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'd2a28ac5-6be5-4a4c-806f-424fc39131ee' LIMIT 
1; ; 
 
2025-09-08 09:21:39,993 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'd2a28ac5-6be5-4a4c-806f-424fc39131ee' 
; 
 
2025-09-08 09:21:40,000 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:21:40,002 [qtp1566353334-165] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【裂纹检查新版流程图】') 
; 
 
2025-09-08 09:21:40,386 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 09:21:40,387 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND disable_flag 
= 0 AND parent_group != '0' AND id != '2' ; 
 
2025-09-08 09:21:40,388 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND parent_group != '0' ; 
 
2025-09-08 09:21:40,389 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group 
= '0' ; 
 
2025-09-08 09:21:40,389 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT id, name, color, code, status, sample_type, parent_group, disable_flag, created_user_id, 
data, samples FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:21:40,495 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:40,498 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:40,563 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:40,565 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:40,577 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:40,579 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:40,597 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:40,599 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:40,612 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:40,614 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:40,627 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:21:40,631 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:21:42,816 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 AND id = 1 LIMIT 1; ; 
 
2025-09-08 09:22:07,762 [async-dispatch-5] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:22:07,765 [async-dispatch-5] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【裂纹检查新版流程图】') 
; 
 
2025-09-08 09:22:14,053 [qtp1566353334-134] INFO  jdbc.sqlonly - INSERT INTO t_widget (widget_id, parent_id, widget_name, data_source, widget_type) VALUES ('6f204a76-9524-45f8-b391-0055aa0c007a','73','二维数组表格裂纹长度检查结果','{"showRowNumber":3,"dataSourceCode":"input_liewenchangdujieguo_doubleArray","colsConfig":[{"colTitle":"\u68c0\u67e5\u8ba1\u6570","typeParam":{"dimensionId":"","options":[],"unitId":""},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_JCJS","correlationCode":"","isEdit":false,"showName":"\u68c0\u67e5\u8ba1\u6570"},{"colTitle":"\u5378\u8f7d\u6bb5\u67d4\u5ea6","typeParam":{"dimensionId":"df6fa213-dc38-45fe-82ee-e94a24c76925","options":[],"unitId":"aad2461a-f13b-400f-95c6-592a965bd846"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_XZDRD","correlationCode":"","isEdit":false,"showName":"\u5378\u8f7d\u6bb5\u67d4\u5ea6"},{"colTitle":"\u53c2\u8003\u5f39\u6027\u6a21\u91cf","typeParam":{"dimensionId":"c15cd6be-8616-4fae-aab8-8cdf67d66f33","options":[],"unitId":"f2006da9-277a-45ba-85f2-331684b02663"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_CKTXML","correlationCode":"","isEdit":false,"showName":"\u53c2\u8003\u5f39\u6027\u6a21\u91cf"},{"colTitle":"\u8ba1\u7b97\u5f39\u6027\u6a21\u91cf","typeParam":{"dimensionId":"a6c87098-71ee-46b2-b940-05f75131b850","options":[],"unitId":"0289d603-874f-4b03-8563-9bd2d855aea2"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_JSTXML","correlationCode":"","isEdit":false,"showName":"\u8ba1\u7b97\u5f39\u6027\u6a21\u91cf"},{"colTitle":"\u53c2\u8003\u88c2\u7eb9\u957f\u5ea6","typeParam":{"dimensionId":"6420a172-7a93-45c6-a8da-7ccb275a1aad","options":[],"unitId":"e655c1f0-26c0-41aa-9d1d-25c94c059d2e"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_CKLWCD","correlationCode":"","isEdit":false,"showName":"\u53c2\u8003\u88c2\u7eb9\u957f\u5ea6"},{"colTitle":"\u8ba1\u7b97\u88c2\u7eb9\u957f\u5ea6","typeParam":{"dimensionId":"6420a172-7a93-45c6-a8da-7ccb275a1aad","options":[],"unitId":"e655c1f0-26c0-41aa-9d1d-25c94c059d2e"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_JSLWCD","correlationCode":"","isEdit":false,"showName":"\u8ba1\u7b97\u88c2\u7eb9\u957f\u5ea6"}],"dataShowType":"RowNumber","updateFreq":180}','doubleArrayTable') 
ON CONFLICT(widget_id) DO UPDATE SET `widget_id` = excluded.widget_id,`parent_id` = excluded.parent_id,`widget_name` 
= excluded.widget_name,`data_source` = excluded.data_source,`widget_type` = excluded.widget_type 
; 
 
2025-09-08 09:22:14,075 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:22:19,300 [qtp1566353334-165] INFO  jdbc.sqlonly - INSERT INTO t_widget (widget_id, parent_id, widget_name, data_source, widget_type) VALUES ('6f204a76-9524-45f8-b391-0055aa0c007a','73','二维数组表格裂纹长度检查结果','{"showRowNumber":3,"dataSourceCode":"input_liewenchangdujieguo_doubleArray","colsConfig":[{"colTitle":"\u68c0\u67e5\u8ba1\u6570","typeParam":{"dimensionId":"","options":[],"unitId":""},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_JCJS","correlationCode":"","isEdit":false,"showName":"\u68c0\u67e5\u8ba1\u6570"},{"colTitle":"\u5378\u8f7d\u6bb5\u67d4\u5ea6","typeParam":{"dimensionId":"df6fa213-dc38-45fe-82ee-e94a24c76925","options":[],"unitId":"aad2461a-f13b-400f-95c6-592a965bd846"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_XZDRD","correlationCode":"","isEdit":false,"showName":"\u5378\u8f7d\u6bb5\u67d4\u5ea6"},{"colTitle":"\u53c2\u8003\u5f39\u6027\u6a21\u91cf","typeParam":{"dimensionId":"c15cd6be-8616-4fae-aab8-8cdf67d66f33","options":[],"unitId":"f2006da9-277a-45ba-85f2-331684b02663"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_CKTXML","correlationCode":"","isEdit":false,"showName":"\u53c2\u8003\u5f39\u6027\u6a21\u91cf"},{"colTitle":"\u8ba1\u7b97\u5f39\u6027\u6a21\u91cf","typeParam":{"dimensionId":"a6c87098-71ee-46b2-b940-05f75131b850","options":[],"unitId":"0289d603-874f-4b03-8563-9bd2d855aea2"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_JSTXML","correlationCode":"","isEdit":false,"showName":"\u8ba1\u7b97\u5f39\u6027\u6a21\u91cf"},{"colTitle":"\u53c2\u8003\u88c2\u7eb9\u957f\u5ea6","typeParam":{"dimensionId":"6420a172-7a93-45c6-a8da-7ccb275a1aad","options":[],"unitId":"e655c1f0-26c0-41aa-9d1d-25c94c059d2e"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_CKLWCD","correlationCode":"","isEdit":false,"showName":"\u53c2\u8003\u88c2\u7eb9\u957f\u5ea6"},{"colTitle":"\u8ba1\u7b97\u88c2\u7eb9\u957f\u5ea6","typeParam":{"dimensionId":"6420a172-7a93-45c6-a8da-7ccb275a1aad","options":[],"unitId":"e655c1f0-26c0-41aa-9d1d-25c94c059d2e"},"isTriggerAction":false,"type":"Number","openCorrelation":false,"code":"LW_JSLWCD","correlationCode":"","isEdit":false,"showName":"\u8ba1\u7b97\u88c2\u7eb9\u957f\u5ea6"}],"dataShowType":"RowNumber","updateFreq":900}','doubleArrayTable') 
ON CONFLICT(widget_id) DO UPDATE SET `widget_id` = excluded.widget_id,`parent_id` = excluded.parent_id,`widget_name` 
= excluded.widget_name,`data_source` = excluded.data_source,`widget_type` = excluded.widget_type 
; 
 
2025-09-08 09:22:19,322 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 09:22:45,971 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= 'd2a28ac5-6be5-4a4c-806f-424fc39131ee' ; 
 
2025-09-08 09:22:45,996 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'd2a28ac5-6be5-4a4c-806f-424fc39131ee' LIMIT 
1; ; 
 
2025-09-08 09:22:45,998 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'd2a28ac5-6be5-4a4c-806f-424fc39131ee' 
; 
 
2025-09-08 09:22:46,004 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:22:46,005 [qtp1566353334-166] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【裂纹检查新版流程图】') 
; 
 
2025-09-08 09:22:46,389 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 09:22:46,390 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND disable_flag 
= 0 AND parent_group != '0' AND id != '2' ; 
 
2025-09-08 09:22:46,390 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND parent_group != '0' ; 
 
2025-09-08 09:22:46,391 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group 
= '0' ; 
 
2025-09-08 09:22:46,392 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id, name, color, code, status, sample_type, parent_group, disable_flag, created_user_id, 
data, samples FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:22:46,462 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:22:46,463 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:22:46,493 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:22:46,494 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:22:46,537 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:22:46,539 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:22:46,616 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:22:46,619 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:22:46,683 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:22:46,685 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:22:46,736 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_isXieBo' and delete_flag = false; 
 
2025-09-08 09:22:46,739 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_isXieBo' and delete_flag 
= false; 
 
2025-09-08 09:22:48,787 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 AND id = 1 LIMIT 1; ; 
 
2025-09-08 09:23:21,755 [async-dispatch-15] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:23:21,757 [async-dispatch-15] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【裂纹检查新版流程图】') 
; 
 
2025-09-08 09:25:41,037 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= 'fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f' ; 
 
2025-09-08 09:25:41,063 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f' LIMIT 
1; ; 
 
2025-09-08 09:25:41,063 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f' 
; 
 
2025-09-08 09:25:41,065 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:25:41,067 [qtp1566353334-165] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【预制裂纹模拟曲线】') 
; 
 
2025-09-08 09:25:41,117 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = '952ef7e6-11e2-4006-a1dd-ab5ac9fecd98' LIMIT 
1; ; 
 
2025-09-08 09:25:41,118 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = '952ef7e6-11e2-4006-a1dd-ab5ac9fecd98' 
; 
 
2025-09-08 09:25:41,119 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:25:41,120 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【试样校验】') 
; 
 
2025-09-08 09:25:41,363 [async-dispatch-3] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:25:41,365 [async-dispatch-3] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【试样校验】') 
; 
 
2025-09-08 09:25:41,565 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units WHERE 1 = 1 AND delete_flag = 0 AND code = 'unit_cdw4b9c1d4' LIMIT 1; 
; 
 
2025-09-08 09:25:41,565 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units_dimension WHERE 1 = 1 AND delete_flag = 0 AND code = 'dimension_Displacement' 
LIMIT 1; ; 
 
2025-09-08 09:25:41,565 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_precast_crack_peak' and delete_flag = false; 
 
2025-09-08 09:25:41,568 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":"e655c1f0-26c0-41aa-9d1d-25c94c059d2e","value":17.25624758842134,"isConstant":0,"value_type":"string","unitType":"6420a172-7a93-45c6-a8da-7ccb275a1aad","type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_precast_crack_peak' and delete_flag 
= false; 
 
2025-09-08 09:25:41,571 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units WHERE 1 = 1 AND delete_flag = 0 AND code = 'unit_cdw4b9c1d4' LIMIT 1; 
; 
 
2025-09-08 09:25:41,572 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units_dimension WHERE 1 = 1 AND delete_flag = 0 AND code = 'dimension_Displacement' 
LIMIT 1; ; 
 
2025-09-08 09:25:41,572 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_precast_crack_valley' and delete_flag = false; 
 
2025-09-08 09:25:41,573 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":"e655c1f0-26c0-41aa-9d1d-25c94c059d2e","value":1.7256247588421338,"isConstant":0,"value_type":"string","unitType":"6420a172-7a93-45c6-a8da-7ccb275a1aad","type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_precast_crack_valley' and 
delete_flag = false; 
 
2025-09-08 09:25:41,597 [async-dispatch-14] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:25:41,599 [async-dispatch-14] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【预制裂纹模拟曲线】') 
; 
 
2025-09-08 09:25:43,628 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= 'ab56adb1-93c8-4ca0-a353-39b5c7a3ed16' ; 
 
2025-09-08 09:25:43,652 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'ab56adb1-93c8-4ca0-a353-39b5c7a3ed16' LIMIT 
1; ; 
 
2025-09-08 09:25:43,658 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'ab56adb1-93c8-4ca0-a353-39b5c7a3ed16' 
; 
 
2025-09-08 09:25:43,667 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 09:25:43,669 [qtp1566353334-165] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【预制裂纹流程图】') 
; 
 
2025-09-08 09:25:43,789 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwcsts' and delete_flag = false; 
 
2025-09-08 09:25:43,791 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":"\u8bf7\u786e\u8ba4\u662f\u5426\u4f7f\u7528\u8bd5\u6837147\u8fdb\u884c\u5e73\u9762\u5e94\u53d8\u65ad\u88c2\u97e7\u5ea6\u8bd5\u9a8c 
\u9884\u5236\u75b2\u52b3\u88c2\u7eb9\u8bd5\u9a8c\uff1f","isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwcsts' and delete_flag 
= false; 
 
2025-09-08 09:25:43,886 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:25:45,109 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_three' and delete_flag = false; 
 
2025-09-08 09:25:45,111 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"value_type":"string","unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_three' and delete_flag = 
false; 
 
2025-09-08 09:25:45,159 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwbs' and delete_flag = false; 
 
2025-09-08 09:25:45,161 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwbs' and delete_flag = 
false; 
 
2025-09-08 09:25:45,298 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_dqzszqs' and delete_flag = false; 
 
2025-09-08 09:25:45,300 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":0,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_dqzszqs' and delete_flag 
= false; 
 
2025-09-08 09:25:45,303 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units WHERE 1 = 1 AND delete_flag = 0 AND code = 'unit_cdw178e4a3' LIMIT 1; 
; 
 
2025-09-08 09:25:45,305 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units_dimension WHERE 1 = 1 AND delete_flag = 0 AND code = 'dimension_cycle' 
LIMIT 1; ; 
 
2025-09-08 09:25:45,305 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_starting_cycle' and delete_flag = false; 
 
2025-09-08 09:25:45,307 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":"fecae037-0413-41dc-9276-f59ef1570121","value":0,"type":null,"isConstant":0,"value_type":"string","unitType":"3b533dda-6074-4ec2-8027-140b8c352072"}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_starting_cycle' and delete_flag 
= false; 
 
2025-09-08 09:25:45,417 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 09:25:45,417 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND disable_flag 
= 0 AND parent_group != '0' AND id != '2' ; 
 
2025-09-08 09:25:45,418 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND parent_group != '0' ; 
 
2025-09-08 09:25:45,419 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group 
= '0' ; 
 
2025-09-08 09:25:45,419 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT id, name, color, code, status, sample_type, parent_group, disable_flag, created_user_id, 
data, samples FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 09:25:45,919 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_valley' and delete_flag = false; 
 
2025-09-08 09:25:45,921 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":1.6896318324019572,"isConstant":0,"value_type":"string","unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_valley' and delete_flag 
= false; 
 
2025-09-08 09:25:45,924 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlw_peak' and delete_flag = false; 
 
2025-09-08 09:25:45,925 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":16.89631832401957,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlw_peak' and delete_flag 
= false; 
 
2025-09-08 09:25:45,929 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwxhcs' and delete_flag = false; 
 
2025-09-08 09:25:45,930 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":10000000,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwxhcs' and delete_flag 
= false; 
 
2025-09-08 09:25:46,262 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_one' and delete_flag = false; 
 
2025-09-08 09:25:46,263 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"value_type":"string","unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_one' and delete_flag = false; 
 
2025-09-08 09:25:46,712 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 AND id = 1 LIMIT 1; ; 
 
2025-09-08 09:25:48,230 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_allKmax' and delete_flag = false; 
 
2025-09-08 09:25:48,232 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":850.8265709574918,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_allKmax' and delete_flag 
= false; 
 
2025-09-08 10:20:40,423 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 AND run_on_stopup = 1 ; 
 
2025-09-08 10:20:40,425 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'a5112cfc-f9ee-4232-86e0-ff1e81e9df45' LIMIT 
1; ; 
 
2025-09-08 10:20:40,426 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'a5112cfc-f9ee-4232-86e0-ff1e81e9df45' 
; 
 
2025-09-08 10:20:40,430 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,431 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【关闭项目时默认执行动作】') 
; 
 
2025-09-08 10:20:40,496 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = '63526397-7d5a-4d20-8840-4fa2ceee596d' LIMIT 
1; ; 
 
2025-09-08 10:20:40,497 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = '63526397-7d5a-4d20-8840-4fa2ceee596d' 
; 
 
2025-09-08 10:20:40,499 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,501 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【关闭项目重置参数】') 
; 
 
2025-09-08 10:20:40,549 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (content) VALUES ( '删除了【关闭项目 project_31】项目') ; 
 
2025-09-08 10:20:40,550 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,553 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,554 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【打开项目默认执行动作】') 
; 
 
2025-09-08 10:20:40,557 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,559 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【终止裂纹长度检查】') 
; 
 
2025-09-08 10:20:40,563 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,565 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【启动】') 
; 
 
2025-09-08 10:20:40,573 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,574 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【横梁停止】') 
; 
 
2025-09-08 10:20:40,577 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,578 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【关闭项目时默认执行动作】') 
; 
 
2025-09-08 10:20:40,666 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,667 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【试样校验】') 
; 
 
2025-09-08 10:20:40,669 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,670 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【联机】') 
; 
 
2025-09-08 10:20:40,738 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,741 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【预制裂纹模拟曲线】') 
; 
 
2025-09-08 10:20:40,745 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,748 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【裂纹检查新版流程图】') 
; 
 
2025-09-08 10:20:40,751 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,752 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【关闭项目重置参数】') 
; 
 
2025-09-08 10:20:40,837 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,840 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【预制裂纹流程图】') 
; 
 
2025-09-08 10:20:40,903 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:40,904 [qtp1566353334-138] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【终止预制裂纹】') 
; 
 
2025-09-08 10:20:41,423 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1=1 AND delete_flag = 0 AND project_name IN () AND project_id 
NOT IN (31) ; 
 
2025-09-08 10:20:41,423 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:41,423 [async-dispatch-10] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(PASSIVE) 
 
2025-09-08 10:20:41,536 [async-dispatch-10] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(RESTART) 
 
2025-09-08 10:20:41,536 [async-dispatch-10] INFO  jdbc.sqlonly - PRAGMA optimize 
 
2025-09-08 10:20:41,538 [async-dispatch-10] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(PASSIVE) 
 
2025-09-08 10:20:41,646 [async-dispatch-10] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(RESTART) 
 
2025-09-08 10:20:41,646 [async-dispatch-10] INFO  jdbc.sqlonly - PRAGMA optimize 
 
2025-09-08 10:20:41,649 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:41,652 [async-dispatch-10] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '保存了项目') ; 
 
2025-09-08 10:20:41,653 [async-dispatch-10] INFO  jdbc.sqlonly - UPDATE t_project_db SET updated_time = '2025-09-08 10:20:41' WHERE project_id = 31 ; 
 
2025-09-08 10:20:41,654 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT * FROM t_variable_input WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 10:20:41,688 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT * FROM t_variable_result WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 10:20:41,689 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 10:20:41,690 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT * FROM t_sample WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 10:20:41,691 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT * FROM t_sample_parameter WHERE delete_flag = false AND sample_id = 'ef1539f0-cde5-4644-b8bd-12268ce19793' 
ORDER BY created_time ASC ; 
 
2025-09-08 10:20:41,692 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT * FROM t_sample_parameter WHERE delete_flag = false AND sample_id = 'faaaa94a-6f84-4ecb-ba47-cac5c2511b71' 
ORDER BY created_time ASC ; 
 
2025-09-08 10:20:41,693 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE delete_flag = false ORDER BY created_time ASC ; 
 
2025-09-08 10:20:41,697 [async-dispatch-10] INFO  jdbc.sqlonly - INSERT INTO t_template_comparison_signature (type, signature) VALUES ('input','9fbc4ac03c9d8da7773d56214fe5b966d3dbc97f33f266aad0460cd3999e2ee3'),('result','64fc7d10d3d9100b6ca30bb38e8b62c96851893a372e63e5eed464c3aecdf52c'),('signal','1c9a06f8de41109c9109697b26b79edbf2414f3d1f11234acd649c7645c758ff'),('sample-type','fabfe042f5b2060ccd3a147f8521b8d15d7ca686d4ed70b4ff61785a594aa5ad'),('action','f3a75c58f1da9ba29b83b5ea4505ca8ee1368f5b4ec9f6cfe85c29539108652f') 
ON CONFLICT(type) DO UPDATE SET `type` = excluded.type,`signature` = excluded.signature ; 
 
2025-09-08 10:20:42,284 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_dqzszqs' and delete_flag = false; 
 
2025-09-08 10:20:42,288 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":null,"value":47173,"isConstant":0,"value_type":"string","unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_dqzszqs' and delete_flag 
= false; 
 
2025-09-08 10:20:42,291 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units WHERE 1 = 1 AND delete_flag = 0 AND code = 'unit_cdw178e4a3' LIMIT 1; 
; 
 
2025-09-08 10:20:42,292 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT id FROM t_units_dimension WHERE 1 = 1 AND delete_flag = 0 AND code = 'dimension_cycle' 
LIMIT 1; ; 
 
2025-09-08 10:20:42,292 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_starting_cycle' and delete_flag = false; 
 
2025-09-08 10:20:42,293 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"unit":"fecae037-0413-41dc-9276-f59ef1570121","value":47173,"type":null,"isConstant":0,"value_type":"string","unitType":"3b533dda-6074-4ec2-8027-140b8c352072"}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_starting_cycle' and delete_flag 
= false; 
 
2025-09-08 10:20:42,601 [qtp1566353334-138] INFO  jdbc.sqlonly - PRAGMA wal_checkpoint(PASSIVE) 
 
2025-09-08 10:20:42,696 [qtp1566353334-134] INFO  jdbc.sqlonly - UPDATE t_station_project SET delete_flag = 1,updated_user_id = 1 WHERE 1=1 and project_id = 
31 ; 
 
2025-09-08 10:20:42,720 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:20:42,722 [qtp1566353334-138] INFO  jdbc.sqlonly - PRAGMA cache_size = -16000 
 
2025-09-08 10:20:42,722 [qtp1566353334-138] INFO  jdbc.sqlonly - PRAGMA journal_mode = WAL 
 
2025-09-08 10:20:42,722 [qtp1566353334-138] INFO  jdbc.sqlonly - PRAGMA synchronous = NORMAL 
 
2025-09-08 10:20:42,722 [qtp1566353334-138] INFO  jdbc.sqlonly - PRAGMA temp_store = MEMORY 
 
2025-09-08 10:20:42,722 [qtp1566353334-138] INFO  jdbc.sqlonly - PRAGMA mmap_size = 134217728 
 
2025-09-08 10:20:42,722 [qtp1566353334-138] INFO  jdbc.sqlonly - PRAGMA page_size = 4096 
 
2025-09-08 10:20:42,722 [qtp1566353334-138] INFO  jdbc.sqlonly - PRAGMA auto_vacuum = INCREMENTAL 
 
2025-09-08 10:20:42,724 [qtp1566353334-138] INFO  jdbc.sqlonly - UPDATE t_mapping SET cfg_id = NULL ; 
 
2025-09-08 10:20:42,764 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:20:42,765 [qtp1566353334-165] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:20:42,789 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT sample_id,sample_name,img,code,created_user_id FROM t_sample WHERE delete_flag = false; 
 
2025-09-08 10:20:42,792 [qtp1566353334-164] INFO  jdbc.sqlonly - SELECT tsp.parameter_id,tsp.parameter_name,tsp.code,tsp.delete_flag,tsp.created_user_id, tsp.sample_id,tsp.order_num,tsp.dimension_id,tsp.units_id,tsp.default_val,tsp.parameter_img, 
tsp.func,tsp.abbreviation,tsp.hidden_flag, tsp.data_type,tsp.select_options,tsp.is_visible_func,tsp.is_disabled_func 
FROM t_sample_parameter tsp WHERE delete_flag = false; 
 
2025-09-08 10:20:42,829 [qtp1566353334-207] INFO  jdbc.sqlonly - SELECT id,name,code,default_unit_id,created_user_id,delete_flag FROM t_units_dimension WHERE 
delete_flag = false ORDER BY created_time; 
 
2025-09-08 10:20:42,829 [qtp1566353334-207] INFO  jdbc.sqlonly - SELECT tu.id,tu.name,tu.code,tu.dimension_id,tu.created_user_id, tu.order_num,tu.visible,tu.proportion,tu.standard_id,tu.delete_flag, 
tus.name standard,tud.name dimension FROM t_units tu LEFT JOIN t_units_standard tus ON tu.standard_id 
= tus.id LEFT JOIN t_units_dimension tud ON tu.dimension_id = tud.id WHERE tu.delete_flag = 
false ; 
 
2025-09-08 10:20:42,855 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 10:20:42,858 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:20:42,881 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_module_datasource WHERE delete_flag = false ; 
 
2025-09-08 10:20:42,904 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:20:42,906 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 10:20:44,132 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT DISTINCT language_name FROM t_international WHERE delete_flag = false AND language_name 
IS NOT null; 
 
2025-09-08 10:25:42,474 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 10:25:42,513 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_ccss WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,514 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_ccss WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,514 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_axis WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,514 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_channel WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,519 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_device WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,603 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_sys_user WHERE delete_flag = 0 AND account = 'admin' AND password = '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918' 
 
2025-09-08 10:25:42,603 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT power,name,used_templates FROM t_sys_role WHERE delete_flag = 0 AND id = 1 
 
2025-09-08 10:25:42,604 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT tsu.id,tsu.name,tsu.role_id,tsu.account,tsr.name as role_name FROM t_sys_user tsu LEFT 
JOIN t_sys_role tsr ON tsu.role_id = tsr.id WHERE tsu.delete_flag = false AND tsu.id = 1; 
 
2025-09-08 10:25:42,605 [qtp1566353334-134] INFO  jdbc.sqlonly - INSERT INTO t_log (content) VALUES ( '【超级管理员】admin: 登录') ; 
 
2025-09-08 10:25:42,627 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_station_or_home_layout_config WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:42,661 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:25:42,663 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 10:25:42,720 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT DISTINCT language_name FROM t_international WHERE delete_flag = false AND language_name 
IS NOT null; 
 
2025-09-08 10:25:42,742 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_module_datasource WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,769 [qtp1566353334-214] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,791 [qtp1566353334-135] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference >= 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
2025-09-08 10:25:42,792 [qtp1566353334-135] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference < 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
2025-09-08 10:25:42,817 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT id,name,code,default_unit_id,created_user_id,delete_flag FROM t_units_dimension WHERE 
delete_flag = false ORDER BY created_time; 
 
2025-09-08 10:25:42,817 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT tu.id,tu.name,tu.code,tu.dimension_id,tu.created_user_id, tu.order_num,tu.visible,tu.proportion,tu.standard_id,tu.delete_flag, 
tus.name standard,tud.name dimension FROM t_units tu LEFT JOIN t_units_standard tus ON tu.standard_id 
= tus.id LEFT JOIN t_units_dimension tud ON tu.dimension_id = tud.id WHERE tu.delete_flag = 
false ; 
 
2025-09-08 10:25:42,841 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT id, project_directory FROM t_system_config WHERE 1 = 1 AND id = '1' LIMIT 1; ; 
 
2025-09-08 10:25:42,852 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,854 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:42,876 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:25:42,878 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 10:25:42,903 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:25:42,905 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:42,926 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_ccss WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,927 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_ccss WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,928 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_axis WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,928 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_channel WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,931 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_hardware_device WHERE delete_flag = false ; 
 
2025-09-08 10:25:42,949 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT audio_id, audio_name, audio_type, remark, name_file FROM t_audio WHERE delete_flag = 
false ; 
 
2025-09-08 10:25:42,971 [qtp1566353334-135] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 10:25:42,995 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_station_project WHERE 1 = 1 AND delete_flag = 0 AND station_id = 'global-monitoring-manager' 
LIMIT 1; ; 
 
2025-09-08 10:25:45,060 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:25:45,062 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 10:25:45,074 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:25:45,075 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:45,089 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 10:25:45,091 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:46,238 [qtp1566353334-166] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 10:25:46,268 [qtp1566353334-210] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 10:25:50,023 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false AND station_id = '2d06ec3a-9ecd-422c-a76a-1afc16b47b24' 
; 
 
2025-09-08 10:25:50,024 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 AND id = '2d06ec3a-9ecd-422c-a76a-1afc16b47b24' LIMIT 1; 
; 
 
2025-09-08 10:25:50,049 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:50,050 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE 1 = 1 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' 
LIMIT 1; ; 
 
2025-09-08 10:25:50,078 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:50,079 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE 1 = 1 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' 
LIMIT 1; ; 
 
2025-09-08 10:25:50,082 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:25:50,083 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station_project WHERE 1=1 AND delete_flag = 0 AND station_id = '2d06ec3a-9ecd-422c-a76a-1afc16b47b24' 
AND project_id = 31 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' ; 
 
2025-09-08 10:25:50,083 [qtp1566353334-166] INFO  jdbc.sqlonly - INSERT INTO t_station_project (station_id,cfg_id,project_id,id,created_user_id) VALUES ( '2d06ec3a-9ecd-422c-a76a-1afc16b47b24', 
'0ae471aa-4442-42b2-922c-4fc6ba916b10', 31, '43bf0dc6-58fc-4086-b6a4-f67e20b8e815', 1) ; 
 
2025-09-08 10:25:50,086 [qtp1566353334-166] INFO  jdbc.sqlonly - UPDATE t_mapping SET mapping_context = '{"_hw_device":null,"_servo_axis_sensor":[{"ctrl_cmd_flag":1,"_info_signals":{"_block_line":{"signal_variable_id":41},"_cycles":{"signal_variable_id":6},"_command":{"signal_variable_id":13},"_in_signals":{"signal_variable_id":42},"_timer":{"signal_variable_id":4},"_cmd_frequency":{"signal_variable_id":7}},"children":[{"min":null,"children":null,"unit":null,"name":"\u4f4d\u79fb","init_value":null,"parent_type":"AXIS","type":"AD","parent_ccss_id":null,"variable_name":"\u5df2\u9009\u62e9","channel_id":"15d8d6ca-bea3-457c-b34c-4816a0ae8332","parent_axis_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","max":null,"parent_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","id":"15d8d6ca-bea3-457c-b34c-4816a0ae8332","unit_type":null,"delete_flag":0,"unmatched":false,"signal_variable_id":{"min":12,"max":11,"current":1}},{"min":null,"children":null,"unit":null,"name":"\u8d1f\u8377","init_value":null,"parent_type":"AXIS","type":"AD","parent_ccss_id":null,"variable_name":"\u5df2\u9009\u62e9","channel_id":"0afa6632-42ac-4647-9242-768a47e2ee7f","parent_axis_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","max":null,"parent_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","id":"0afa6632-42ac-4647-9242-768a47e2ee7f","unit_type":null,"delete_flag":0,"unmatched":false,"signal_variable_id":{"min":18,"max":17,"current":2}},{"min":null,"children":null,"unit":null,"name":"\u53d8\u5f62","init_value":null,"parent_type":"AXIS","type":"AD","parent_ccss_id":null,"variable_name":"\u5df2\u9009\u62e9","channel_id":"be1851be-34b7-46e4-96af-8e3202e894c1","parent_axis_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","max":null,"parent_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","id":"be1851be-34b7-46e4-96af-8e3202e894c1","unit_type":null,"delete_flag":0,"unmatched":false,"signal_variable_id":{"min":20,"max":19,"current":3}}],"ctrl_block_cycles_flag":1,"cmd_frequency_flag":1,"name":"\u4f3a\u670d1","parent_type":"ServoAxisSensor","axis_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","type":"ServoAxisSensor","upper_limits_flag":1,"axis_name":"\u4f3a\u670d1","channel_id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","ctrl_output_flag":1,"parent_id":"","id":"ce5e6b00-bc8e-47e1-82b6-847f0397b186","ctrl_cycles_flag":1,"delete_flag":0,"block_line_flag":1,"ad_sensor_count":3,"lower_limits_flag":1,"ctrl_maxmin_flag":1,"unmatched":false,"old_type":"ServoAxisSensor"}],"_output":[],"_temp_axis_sensor":[],"_creep_axis_sensor":[],"_hand_box":[],"_a_d":[],"_d_a":[],"virtual_channel":[{"hw_key":"hardware_simulator","name":"\u4f4d\u79fb\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u4f4d\u79fb\u6700\u5927\u503c","channel_id":5,"parent_axis_id":"","parent_id":"b5f23468-d9b9-4c4d-a2c4-74f074a32ef1","id":5,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":5,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u4f4d\u79fb\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u4f4d\u79fb\u6700\u5c0f\u503c","channel_id":10,"parent_axis_id":"","parent_id":"d405beaa-76f0-408d-bd91-62087a7d8f6d","id":10,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":10,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u5468\u671f","parent_type":"AXIS","variable_name":"\u5468\u671f-\u5468\u671f","channel_id":14,"parent_axis_id":"","parent_id":"8f1cf524-c604-4abd-89bb-96a67ee67bb0","id":14,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":14,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u65f6\u95f4","parent_type":"AXIS","variable_name":"\u5468\u671f-\u65f6\u95f4","channel_id":15,"parent_axis_id":"","parent_id":"5bd195c2-250e-4d74-80c2-f49332479064","id":15,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":15,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u88c2\u7eb9\u957f\u5ea6","channel_id":16,"parent_axis_id":"","parent_id":"5bd195c2-250e-4d74-80c2-f49332479064","id":16,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":16,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u6700\u5927\u503c","channel_id":8,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":8,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":8,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u6700\u5c0f\u503c","channel_id":9,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":9,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":9,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5e73\u5747\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u5e73\u5747\u88c2\u7eb9\u957f\u5ea6","channel_id":21,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":21,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":21,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u75b2\u52b3\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u75b2\u52b3\u88c2\u7eb9\u957f\u5ea6","channel_id":22,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":22,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":22,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","channel_id":23,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":23,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":23,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u540e\u9636\u6bb5\u7684\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u6700\u540e\u9636\u6bb5\u7684\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","channel_id":24,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":24,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":24,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u7ec8\u6700\u5927\u529b","parent_type":"AXIS","variable_name":"\u6700\u7ec8\u6700\u5927\u529b","channel_id":25,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":25,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":25,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u9891\u7387","parent_type":"AXIS","variable_name":"\u5468\u671f-\u9891\u7387","channel_id":26,"parent_axis_id":"","parent_id":"2e6f3899-e4f8-4659-9ccc-b43c277301f2","id":26,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":26,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5f62\u72b6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u5f62\u72b6\u56e0\u5b50","channel_id":27,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":27,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":27,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u6700\u5927\u8d1f\u8377","channel_id":28,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":28,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":28,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5c0f\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u6700\u5c0f\u8d1f\u8377","channel_id":29,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":29,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":29,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u8303\u56f4","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u8303\u56f4","channel_id":30,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":30,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":30,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5c0fK","parent_type":"AXIS","variable_name":"\u6700\u5c0fK","channel_id":32,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":32,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":32,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927K","parent_type":"AXIS","variable_name":"\u6700\u5927K","channel_id":31,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":31,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":31,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"K\u503c\u8303\u56f4","parent_type":"AXIS","variable_name":"K\u503c\u8303\u56f4","channel_id":33,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":33,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":33,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8bd5\u9a8c\u8fdb\u5ea6","parent_type":"AXIS","variable_name":"\u8bd5\u9a8c\u8fdb\u5ea6","channel_id":34,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":34,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":34,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u67d4\u5ea6","parent_type":"AXIS","variable_name":"\u67d4\u5ea6","channel_id":35,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":35,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":35,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u62df\u5408\u4e0a\u9650\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u62df\u5408\u4e0a\u9650\u8d1f\u8377","channel_id":36,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":36,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":36,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u62df\u5408\u4e0b\u9650\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u62df\u5408\u4e0b\u9650\u8d1f\u8377","channel_id":37,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":37,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":37,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u865a\u62df\u5468\u671f","parent_type":"AXIS","variable_name":"\u865a\u62df\u5468\u671f","channel_id":38,"parent_axis_id":"","parent_id":"e18a3dd7-c76e-4f3d-a339-958f8ff08ec9","id":38,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":38,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u53d8\u5f62\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u53d8\u5f62\u6700\u5c0f\u503c","channel_id":39,"parent_axis_id":"","parent_id":"61bb339b-ca15-4dce-a592-70c71903d90b","id":39,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":39,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u53d8\u5f62\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u53d8\u5f62\u6700\u5927\u503c","channel_id":40,"parent_axis_id":"","parent_id":"61bb339b-ca15-4dce-a592-70c71903d90b","id":40,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":40,"virtual_parent_type":"virtualChannel"}],"_input":[]}',usable_resource 
= '{"_hw_device":[],"_servo_axis_sensor":[{"_info_signals":{"_block_line":{"signal_variable_id":41},"_cycles":{"signal_variable_id":6},"_command":{"signal_variable_id":13},"_in_signals":{"signal_variable_id":42},"_timer":{"signal_variable_id":4},"_cmd_frequency":{"signal_variable_id":7}},"hw_key":"hardware_simulator","form_name":"\u4f3a\u670d\u8f742(hardware_simulator)","children":[{"hw_key":"hardware_simulator","min":0,"form_name":"\u4f4d\u79fb","page_id":"1","unit":null,"name":"\u4f4d\u79fb","sensor_name":["Sensor0"],"init_value":null,"parent_type":"AXIS","type":"AD","sensor_count":1,"parent_ccss_id":"hardware_simulator-0000-hw-ccss-id-0000","channel_id":"7189e3ae-78c0-42af-8cb3-5b1abedc0247","parent_axis_id":"7853d594-6516-461f-aa64-0c783f9f60e5","daq_rate":1000,"max":0,"id":"7189e3ae-78c0-42af-8cb3-5b1abedc0247","unit_type":"","delete_flag":0,"idx":0,"signal_variable_id":{"min":12,"max":11,"current":1}},{"hw_key":"hardware_simulator","min":0,"form_name":"\u8d1f\u8377","page_id":"1","unit":null,"name":"\u8d1f\u8377","sensor_name":["\u4f20\u611f\u56681"],"init_value":null,"parent_type":"AXIS","type":"AD","sensor_count":1,"parent_ccss_id":"hardware_simulator-0000-hw-ccss-id-0000","channel_id":"80f17c18-4765-4170-8e9d-6b60d416a6c2","parent_axis_id":"7853d594-6516-461f-aa64-0c783f9f60e5","daq_rate":1000,"max":0,"id":"80f17c18-4765-4170-8e9d-6b60d416a6c2","unit_type":"","delete_flag":0,"idx":1,"signal_variable_id":{"min":18,"max":17,"current":2}},{"hw_key":"hardware_simulator","min":0,"form_name":"\u53d8\u5f62","page_id":"1","unit":null,"name":"\u53d8\u5f62","sensor_name":["\u4f20\u611f\u56682"],"init_value":null,"parent_type":"AXIS","type":"AD","sensor_count":1,"parent_ccss_id":"hardware_simulator-0000-hw-ccss-id-0000","channel_id":"4705269f-1dee-4371-a250-265d5a586971","parent_axis_id":"7853d594-6516-461f-aa64-0c783f9f60e5","daq_rate":1000,"max":0,"id":"4705269f-1dee-4371-a250-265d5a586971","unit_type":"","delete_flag":0,"idx":2,"signal_variable_id":{"min":20,"max":19,"current":3}}],"ctrl_block_cycles_flag":1,"cmd_frequency_flag":1,"name":"\u4f3a\u670d\u8f740","sensor_name":[],"parent_type":"ServoAxisSensor","axis_id":"7853d594-6516-461f-aa64-0c783f9f60e5","type":"ServoAxisSensor","upper_limits_flag":1,"press_up_or_down":1,"tensile_up_or_down":1,"axis_name":"\u4f3a\u670d\u8f740","daq_rate":0,"parent_id":"hardware_simulator-0000-hw-ccss-id-0000","id":"5d692fd6-f30c-4ab9-b91e-f5436d38d1d8","up_direct_value":1,"ctrl_cycles_flag":1,"delete_flag":0,"block_line_flag":1,"ad_sensor_count":10,"idx":0,"lower_limits_flag":1,"signal_variable_id":null}],"_output":[],"_temp_axis_sensor":[],"_creep_axis_sensor":[],"_hand_box":[],"_a_d":[],"_d_a":[],"virtual_channel":[{"hw_key":"hardware_simulator","name":"\u4f4d\u79fb\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u4f4d\u79fb\u6700\u5927\u503c","channel_id":5,"parent_axis_id":"","parent_id":"b5f23468-d9b9-4c4d-a2c4-74f074a32ef1","id":5,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":5,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u4f4d\u79fb\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u4f4d\u79fb\u6700\u5c0f\u503c","channel_id":10,"parent_axis_id":"","parent_id":"d405beaa-76f0-408d-bd91-62087a7d8f6d","id":10,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":10,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u5468\u671f","parent_type":"AXIS","variable_name":"\u5468\u671f-\u5468\u671f","channel_id":14,"parent_axis_id":"","parent_id":"8f1cf524-c604-4abd-89bb-96a67ee67bb0","id":14,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":14,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u65f6\u95f4","parent_type":"AXIS","variable_name":"\u5468\u671f-\u65f6\u95f4","channel_id":15,"parent_axis_id":"","parent_id":"5bd195c2-250e-4d74-80c2-f49332479064","id":15,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":15,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u88c2\u7eb9\u957f\u5ea6","channel_id":16,"parent_axis_id":"","parent_id":"5bd195c2-250e-4d74-80c2-f49332479064","id":16,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":16,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u6700\u5927\u503c","channel_id":8,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":8,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":8,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u6700\u5c0f\u503c","channel_id":9,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":9,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":9,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5e73\u5747\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u5e73\u5747\u88c2\u7eb9\u957f\u5ea6","channel_id":21,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":21,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":21,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u75b2\u52b3\u88c2\u7eb9\u957f\u5ea6","parent_type":"AXIS","variable_name":"\u75b2\u52b3\u88c2\u7eb9\u957f\u5ea6","channel_id":22,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":22,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":22,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","channel_id":23,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":23,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":23,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u540e\u9636\u6bb5\u7684\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u6700\u540e\u9636\u6bb5\u7684\u6700\u5927\u5e94\u529b\u5f3a\u5ea6\u56e0\u5b50","channel_id":24,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":24,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":24,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u7ec8\u6700\u5927\u529b","parent_type":"AXIS","variable_name":"\u6700\u7ec8\u6700\u5927\u529b","channel_id":25,"parent_axis_id":"","parent_id":"a2c6df87-6f79-40bd-af82-d79130eb7d50","id":25,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":25,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5468\u671f-\u9891\u7387","parent_type":"AXIS","variable_name":"\u5468\u671f-\u9891\u7387","channel_id":26,"parent_axis_id":"","parent_id":"2e6f3899-e4f8-4659-9ccc-b43c277301f2","id":26,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":26,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u5f62\u72b6\u56e0\u5b50","parent_type":"AXIS","variable_name":"\u5f62\u72b6\u56e0\u5b50","channel_id":27,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":27,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":27,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u6700\u5927\u8d1f\u8377","channel_id":28,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":28,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":28,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5c0f\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u6700\u5c0f\u8d1f\u8377","channel_id":29,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":29,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":29,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8d1f\u8377\u8303\u56f4","parent_type":"AXIS","variable_name":"\u8d1f\u8377\u8303\u56f4","channel_id":30,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":30,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":30,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5c0fK","parent_type":"AXIS","variable_name":"\u6700\u5c0fK","channel_id":32,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":32,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":32,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u6700\u5927K","parent_type":"AXIS","variable_name":"\u6700\u5927K","channel_id":31,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":31,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":31,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"K\u503c\u8303\u56f4","parent_type":"AXIS","variable_name":"K\u503c\u8303\u56f4","channel_id":33,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":33,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":33,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u8bd5\u9a8c\u8fdb\u5ea6","parent_type":"AXIS","variable_name":"\u8bd5\u9a8c\u8fdb\u5ea6","channel_id":34,"parent_axis_id":"","parent_id":"021def1e-e011-44ce-bfd2-30d880373d45","id":34,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":34,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u67d4\u5ea6","parent_type":"AXIS","variable_name":"\u67d4\u5ea6","channel_id":35,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":35,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":35,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u62df\u5408\u4e0a\u9650\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u62df\u5408\u4e0a\u9650\u8d1f\u8377","channel_id":36,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":36,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":36,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u62df\u5408\u4e0b\u9650\u8d1f\u8377","parent_type":"AXIS","variable_name":"\u62df\u5408\u4e0b\u9650\u8d1f\u8377","channel_id":37,"parent_axis_id":"","parent_id":"d646d3a8-8a43-4076-9def-b02855af7d75","id":37,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":37,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u865a\u62df\u5468\u671f","parent_type":"AXIS","variable_name":"\u865a\u62df\u5468\u671f","channel_id":38,"parent_axis_id":"","parent_id":"e18a3dd7-c76e-4f3d-a339-958f8ff08ec9","id":38,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":38,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u53d8\u5f62\u6700\u5c0f\u503c","parent_type":"AXIS","variable_name":"\u53d8\u5f62\u6700\u5c0f\u503c","channel_id":39,"parent_axis_id":"","parent_id":"61bb339b-ca15-4dce-a592-70c71903d90b","id":39,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":39,"virtual_parent_type":"virtualChannel"},{"hw_key":"hardware_simulator","name":"\u53d8\u5f62\u6700\u5927\u503c","parent_type":"AXIS","variable_name":"\u53d8\u5f62\u6700\u5927\u503c","channel_id":40,"parent_axis_id":"","parent_id":"61bb339b-ca15-4dce-a592-70c71903d90b","id":40,"virtual_channel_index":"1-0","virtual_axle_index":"1","signal_variable_id":40,"virtual_parent_type":"virtualChannel"}],"_input":[]}',mapping_status 
= 'NORMAL',cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' ; 
 
2025-09-08 10:25:50,110 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:25:50,111 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:50,139 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:25:50,163 [qtp1566353334-166] INFO  jdbc.sqlonly - select tpd.*, tsu.name from t_project_db tpd left join t_sys_user tsu on tsu.delete_flag = 
0 and tsu.id = tpd.created_user_id where tpd.delete_flag = 0 order by tpd.created_time desc 
; 
 
2025-09-08 10:25:50,189 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:25:50,191 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 10:25:50,216 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 10:25:50,217 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:50,245 [qtp1566353334-211] INFO  jdbc.sqlonly - INSERT INTO t_log (content) VALUES ( '打开项目 project_31') ; 
 
2025-09-08 10:25:50,245 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:25:50,274 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:25:50,275 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_station_project WHERE 1=1 AND project_id = 31 AND station_id != 'global-monitoring-manager' 
ORDER BY created_time DESC ; 
 
2025-09-08 10:25:50,275 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE delete_flag = false AND parent_group = '0' ; 
 
2025-09-08 10:25:50,276 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND id != '2' AND parent_group 
!= '0' ; 
 
2025-09-08 10:25:50,277 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_result WHERE delete_flag = false ; 
 
2025-09-08 10:25:50,279 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT id, code FROM t_units WHERE 1=1 ; 
 
2025-09-08 10:25:50,280 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT id, code FROM t_units_dimension WHERE 1=1 ; 
 
2025-09-08 10:25:50,281 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT vi.*, sv.variable_val FROM t_variable_input vi LEFT JOIN t_sample_variable sv ON sv.variable_code 
= vi.code WHERE vi.delete_flag = false AND sv.delete_flag = false AND sv.sample_code = 'sample_14785d372'; 
 
2025-09-08 10:25:50,322 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT id, code FROM t_units WHERE 1=1 ; 
 
2025-09-08 10:25:50,324 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT id, code FROM t_units_dimension WHERE 1=1 ; 
 
2025-09-08 10:25:50,325 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_input WHERE 1=1 AND is_overall = 1 ; 
 
2025-09-08 10:25:50,328 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:50,328 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT signal_variable_id, code FROM t_variable_signal WHERE delete_flag = false ; 
 
2025-09-08 10:25:50,330 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE 1 = 1 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' 
LIMIT 1; ; 
 
2025-09-08 10:25:50,331 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 AND id = '2d06ec3a-9ecd-422c-a76a-1afc16b47b24' LIMIT 1; 
; 
 
2025-09-08 10:25:50,333 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:50,334 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:50,336 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 41 
LIMIT 1; ; 
 
2025-09-08 10:25:50,337 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 6 
LIMIT 1; ; 
 
2025-09-08 10:25:50,338 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 13 
LIMIT 1; ; 
 
2025-09-08 10:25:50,338 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 42 
LIMIT 1; ; 
 
2025-09-08 10:25:50,339 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 4 
LIMIT 1; ; 
 
2025-09-08 10:25:50,339 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 7 
LIMIT 1; ; 
 
2025-09-08 10:25:50,340 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 1 
LIMIT 1; ; 
 
2025-09-08 10:25:50,340 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 12 
LIMIT 1; ; 
 
2025-09-08 10:25:50,341 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 11 
LIMIT 1; ; 
 
2025-09-08 10:25:50,341 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 2 
LIMIT 1; ; 
 
2025-09-08 10:25:50,342 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 18 
LIMIT 1; ; 
 
2025-09-08 10:25:50,342 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 17 
LIMIT 1; ; 
 
2025-09-08 10:25:50,343 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 3 
LIMIT 1; ; 
 
2025-09-08 10:25:50,343 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 20 
LIMIT 1; ; 
 
2025-09-08 10:25:50,344 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE 1 = 1 AND delete_flag = 0 AND signal_variable_id = 19 
LIMIT 1; ; 
 
2025-09-08 10:25:50,344 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_flow_chart_data WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:50,345 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT id, params, action_id FROM t_subtask WHERE 1=1 AND action_id = '' ; 
 
2025-09-08 10:25:50,346 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_test_result WHERE delete_flag = false AND type = 'RESULT_VAR' ; 
 
2025-09-08 10:25:50,347 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_result WHERE 1=1 AND delete_flag = 0 AND result_variable_id IN ('0d28f0e1-1393-41f3-9b13-be502bd04584') 
; 
 
2025-09-08 10:25:50,348 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_global_project_mapping WHERE 1=1 AND delete_flag = 0 AND project_id NOTNULL 
; 
 
2025-09-08 10:25:50,349 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT action_id, flow_chart_scheduler FROM t_action WHERE delete_flag = false ; 
 
2025-09-08 10:25:50,351 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT id, params, action_id FROM t_subtask WHERE 1=1 AND action_id IN ('5a5f09ef-c362-437a-9a8a-8e1d86e455d1','679f70fa-d870-40d9-b121-c759b28044ed','aa25639b-ca7e-4f2e-9113-26cc42a12f6c','059424b8-2ff1-43c7-877a-1bda3abf54bf','271168ec-68e7-4807-bbb9-2d1d127feaa7','6ed1a4e6-a797-4b13-a81d-d00b2b141967','437289c3-f33c-4036-98a1-bb7cc6740fb4','858d991a-bade-4ef6-b394-36eb56e64780','334e466d-bdf9-495e-bd95-241aec10008f','6263aa5c-3aed-4e91-809e-706c226041c0','b18f9963-afaa-48a4-a5cb-73a029ffb8c0','0d84683a-47a6-47ea-bb0f-668955dcecdb','c41551f6-2fb2-433e-8d6d-4cac4793c249','bc4eff3c-d154-44ca-aa02-4ae24da241be','d278b617-ff31-41fe-84a6-3ae5e54149da','a048cdfc-ad4d-4108-b3bd-50da91663d64','9df7ca66-77f2-4809-872a-9efc68d048a4','66c287cb-50e4-4269-ae36-a91d4e1d0703','42fd8414-03db-4487-97b3-dedbd644fbda','caebc7dd-7b7b-49c0-9481-3c1f992ef2c1','d3784346-2780-4c48-8403-de57053fe23e','ac20609c-30be-473e-a9d3-bf39e1bd764a','d39e360f-acf5-4e3b-94d2-107cd5938491','fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f','ab56adb1-93c8-4ca0-a353-39b5c7a3ed16','867242e3-2471-4371-82fa-24db7253e028','35a50d1d-e1b9-4405-8f55-51b2d7e46789','e2c18c1e-62c7-427e-af54-3bd41ce5f8b8','a67517fc-f893-4b43-b7bf-2bdb16325bf4','4d8c8e73-c0e6-4500-952b-3e3cee815f39','ba1bec2b-af91-4e2c-8815-e685ff1c878b','e0449d34-2a68-4903-b755-0777eff578e8','01ae702e-513c-422a-822f-536178aaf22d','d2a28ac5-6be5-4a4c-806f-424fc39131ee','5f971f06-8061-402b-a27b-573a9a8a4b3b','40799e80-4904-461d-81c3-78d5292c0237','fbc2d565-2a68-473a-a2b5-7e932c5ae3a7','db4ae573-ed87-4f2c-b0a5-8c2f7e191983','facf71e7-e2ad-4e4c-9dc7-ee4c8c3e07cd','d387206f-efe9-4d8b-8020-38ece5121f0f','3601749f-b58b-4018-b8c3-e1bfc760f8e0','b8180c37-afe6-4b02-8fe6-5298a14b54f9','2e2782db-c485-4615-9808-4488b7007895','da399466-da97-449d-b998-0d7a0823cdd0','541ef62d-e173-4963-9a96-1b55f07523b7','6a579b71-e5fb-4bef-9569-33de699c61ea','a5112cfc-f9ee-4232-86e0-ff1e81e9df45','da633f41-5a7a-4b48-aefc-9c7e63a3e8a8','50753527-8b0e-419d-9ba2-c09916053748','0435e7a9-00cc-4721-81bf-af9843d16737','bb24c523-e5ac-4a81-8891-b28c75583058','a20de5f2-23db-42ea-bae7-7395a4742665','244c8013-2b7f-4254-af35-97709d2f3fca','4db1c6fe-8ae5-4b2d-ae0c-5fcc249d9b91','d6264fcd-fc87-4ba5-a8af-35236f156425','ec6e0760-ab6d-496e-bdd5-292be45ed606','e3ae9b9c-235f-4820-8de9-bbcfaa8a9efd','6ccb63fa-c223-4d35-861f-008c33cbbb9f','6b0632c6-80b1-4da2-88f8-81d49a6508a4','41c80162-da78-4d91-8a50-e19d59781b7e','e3c8ca6f-23bf-4fc6-8495-da6d17924867','c496d650-fe66-484c-b9c7-a901ff779cdb','63526397-7d5a-4d20-8840-4fa2ceee596d','b0dddc3a-5b8d-40fc-befd-37a4e03f9cf7','952ef7e6-11e2-4006-a1dd-ab5ac9fecd98','c20d06d7-fcb8-4a98-8cec-b559d461c8d6','523975db-051c-4497-9ad9-1634f1e5ff1d') 
; 
 
2025-09-08 10:25:50,366 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_flow_chart_data WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:50,366 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_input WHERE delete_flag = 0 AND (json_extract(program_tab, '$.isVisible') 
!= '' OR json_extract(program_tab, '$.isDisabled') != '' OR json_extract(program_tab, '$.mode') 
!= '' OR json_extract(program_tab, '$.isCheck') != '' OR json_extract(program_tab, '$.isCheckdisabledr') 
!= ''); 
 
2025-09-08 10:25:50,369 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_function WHERE delete_flag = false ; 
 
2025-09-08 10:25:50,370 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE delete_flag = false ; 
 
2025-09-08 10:25:50,371 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT id, code FROM t_units WHERE 1=1 AND id IN ('e655c1f0-26c0-41aa-9d1d-25c94c059d2e','f7d1bcc0-cec4-4118-bb30-36b2995893d4','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','f9b2c0be-a829-4072-878e-f5f592308f79','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','02ad4c18-698f-4b25-935d-3f0501c2e1fb','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','','f9b2c0be-a829-4072-878e-f5f592308f79','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','e655c1f0-26c0-41aa-9d1d-25c94c059d2e','fa93dbf6-960a-4191-a70a-6ae57061150b','fa93dbf6-960a-4191-a70a-6ae57061150b','f7d1bcc0-cec4-4118-bb30-36b2995893d4','02ad4c18-698f-4b25-935d-3f0501c2e1fb','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','f7d1bcc0-cec4-4118-bb30-36b2995893d4','85b70b7c-37cf-43b4-ba98-d27dc71efd1a','85b70b7c-37cf-43b4-ba98-d27dc71efd1a','85b70b7c-37cf-43b4-ba98-d27dc71efd1a','784b102e-a8a5-4943-9f6e-f720f29f0327','fecae037-0413-41dc-9276-f59ef1570121') 
; 
 
2025-09-08 10:25:50,372 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT id, code FROM t_units_dimension WHERE 1=1 AND id IN ('6420a172-7a93-45c6-a8da-7ccb275a1aad','56019f36-d734-40c0-8872-73b60304f80a','6420a172-7a93-45c6-a8da-7ccb275a1aad','54891d75-4375-4253-8a5b-068423501a0a','6420a172-7a93-45c6-a8da-7ccb275a1aad','fed22294-bb3a-4cfe-b832-a5a49a4f80c7','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','','54891d75-4375-4253-8a5b-068423501a0a','6420a172-7a93-45c6-a8da-7ccb275a1aad','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','6420a172-7a93-45c6-a8da-7ccb275a1aad','ebc759ee-b54c-489a-ac6d-0f8f892d15ae','ebc759ee-b54c-489a-ac6d-0f8f892d15ae','56019f36-d734-40c0-8872-73b60304f80a','fed22294-bb3a-4cfe-b832-a5a49a4f80c7','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','56019f36-d734-40c0-8872-73b60304f80a','b757382f-2c6a-4f32-9630-413273c69050','b757382f-2c6a-4f32-9630-413273c69050','b757382f-2c6a-4f32-9630-413273c69050','7fa7a72d-541c-49b6-9aaf-cf2873abbf7e','3b533dda-6074-4ec2-8027-140b8c352072') 
; 
 
2025-09-08 10:25:50,373 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:50,375 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:25:50,375 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:25:51,453 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:51,454 [qtp1566353334-211] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE 1 = 1 AND cfg_id = '0ae471aa-4442-42b2-922c-4fc6ba916b10' 
LIMIT 1; ; 
 
2025-09-08 10:25:51,477 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_page WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,478 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '1' AND binder_id IS 
NULL ; 
 
2025-09-08 10:25:51,478 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,479 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '2' AND binder_id IS 
NULL ; 
 
2025-09-08 10:25:51,480 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,481 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '3' AND binder_id IS 
NULL ; 
 
2025-09-08 10:25:51,482 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,483 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '1e46879c-298c-4491-bafe-2f04759775a9' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,483 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,484 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '0bc61403-d55a-4596-8be4-91157755902e' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,485 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,486 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '21cf43c1-0646-48ad-9f06-f04ef7396cdd' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,487 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,488 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '403b1882-f132-4b90-a1a0-0c22b0d0bd25' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,488 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,489 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '93532d6d-d611-4e79-933b-bc77a07c6d79' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,489 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,490 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = 'e224fbfb-d760-4fd5-b2ec-42b7c90a775f' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,491 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,529 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_page WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,530 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '1' AND binder_id IS 
NULL ; 
 
2025-09-08 10:25:51,531 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,532 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '2' AND binder_id IS 
NULL ; 
 
2025-09-08 10:25:51,532 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,534 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '3' AND binder_id IS 
NULL ; 
 
2025-09-08 10:25:51,535 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,535 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '1e46879c-298c-4491-bafe-2f04759775a9' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,536 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,537 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '0bc61403-d55a-4596-8be4-91157755902e' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,537 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,538 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '21cf43c1-0646-48ad-9f06-f04ef7396cdd' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,538 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,539 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '403b1882-f132-4b90-a1a0-0c22b0d0bd25' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,540 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,541 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = '93532d6d-d611-4e79-933b-bc77a07c6d79' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,541 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,542 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND page_id = 'e224fbfb-d760-4fd5-b2ec-42b7c90a775f' 
AND binder_id IS NULL ; 
 
2025-09-08 10:25:51,543 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,559 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,561 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal_group WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,585 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT shortcut_id, shortcut_name, icon, show_type, dialog_id, order_num, shortcut_key, action_id, 
power, halving_line, created_user_id, program_tab, input_code, tip, gap_flag, need_login_check 
FROM t_shortcut WHERE delete_flag = false ORDER BY order_num ASC ; 
 
2025-09-08 10:25:51,613 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 10:25:51,614 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND disable_flag 
= 0 AND parent_group != '0' AND id != '2' ; 
 
2025-09-08 10:25:51,615 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND parent_group != '0' ; 
 
2025-09-08 10:25:51,616 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group 
= '0' ; 
 
2025-09-08 10:25:51,617 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT id, name, color, code, status, sample_type, parent_group, disable_flag, created_user_id, 
data, samples FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,640 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,667 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT id, widget_id, data FROM t_widget_data_source WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,678 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_test_result WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,706 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT trv.result_variable_id,trv.variable_name,trv.type,trv.code, trv.abbreviation,tu.name 
unit_name, trv.created_user_id,trv.signal_var_id,trv.display_modes,trv.format_type,trv.format_info,trv.dimension_id,trv.unit_id,trv.locked_unit_ids, 
trv.auxiliary_line_flag, trv.auxiliary_line_arr, trv.marking_flag, trv.marking_count, trv.marking_action, 
trv.description FROM t_variable_result trv LEFT JOIN t_units tu ON trv.unit_id = tu.id AND 
tu.delete_flag = false WHERE trv.delete_flag = false ; 
 
2025-09-08 10:25:51,708 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'cc34a725-fc60-4d44-8098-05319e06cab7' 
AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,710 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'aa35ee11-dd20-4385-b76a-9d5edd8a8c0f' 
AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,711 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'a60e09cc-f711-4042-a1e9-29511b11b414' 
AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,713 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = '3a28d0eb-423b-4dfb-8e7c-c36ff1cdfc35' 
AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,714 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = '0b90ea68-ac19-4202-8ab8-411492f6a207' 
AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,715 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'd9f20c00-926e-4a0e-b122-642c25aa02b1' 
AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,716 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'fcd4dbf2-7654-4474-a678-4d79eb25864c' 
AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,717 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = 'b6b16edb-801f-4195-b0cc-87f158abc572' 
AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,718 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT code, default_val FROM t_variable_input WHERE 1=1 AND related_result_variable_id = '51a798fc-195e-402c-88b8-41b055fdd404' 
AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,731 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:51,732 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE 1=1 AND delete_flag = 0 AND sample_code = 'sample_14785d372' 
; 
 
2025-09-08 10:25:51,738 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT id, name, group_category, code, variable_type, default_val, number_tab, f1_index, is_enable, 
is_feature, is_fx, created_user_id, type, is_overall, contact_code, description, pic, related_result_variable_id, 
related_sub_task_id, default_val, number_tab, reasonable_val_tab, button_tab, button_variable_tab, 
program_tab, text_tab, boolean_tab, select_tab, two_digit_array_tab, control_tab, label_tab, 
picture_tab, related_var_tab, buffer_tab, custom_array_tab, double_array_tab, double_array_list_tab 
FROM t_variable_input WHERE 1=1 AND delete_flag = 0 AND name LIKE '%%' ORDER BY created_time 
DESC ; 
 
2025-09-08 10:25:51,827 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_picture WHERE 1 = 1 AND id = '85b303d7-5008-4bec-9429-f75a971bcca3' LIMIT 1; 
; 
 
2025-09-08 10:25:51,982 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_guide_dialog WHERE 1=1 AND delete_flag = 0 AND type = 'general' AND dialog_name 
LIKE '%%' ; 
 
2025-09-08 10:25:51,984 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_guide WHERE delete_flag = false AND type = 'general' ; 
 
2025-09-08 10:25:51,985 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT id, variable_code, general_tab, in_process_tab, not_in_process_tab FROM t_passage_form 
WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:51,985 [qtp1566353334-209] INFO  jdbc.sqlonly - SELECT * FROM t_variable_signal WHERE delete_flag = false ; 
 
2025-09-08 10:25:51,988 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:52,037 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT video_id, video_name, video_type, remark, name_file, sample_code, video_file, current_create_time, 
sample_id FROM t_video WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,040 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_table_config WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:52,049 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_mapping WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:52,082 [qtp1566353334-212] INFO  jdbc.sqlonly - SELECT id,name,code,default_unit_id,created_user_id,delete_flag FROM t_units_dimension WHERE 
delete_flag = false ORDER BY created_time; 
 
2025-09-08 10:25:52,083 [qtp1566353334-212] INFO  jdbc.sqlonly - SELECT tu.id,tu.name,tu.code,tu.dimension_id,tu.created_user_id, tu.order_num,tu.visible,tu.proportion,tu.standard_id,tu.delete_flag, 
tus.name standard,tud.name dimension FROM t_units tu LEFT JOIN t_units_standard tus ON tu.standard_id 
= tus.id LEFT JOIN t_units_dimension tud ON tu.dimension_id = tud.id WHERE tu.delete_flag = 
false ; 
 
2025-09-08 10:25:52,111 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_export_config WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,140 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT id, name, code, func FROM t_sample_instance_about WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:52,172 [qtp1566353334-209] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 10:25:52,210 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_double_array_curve WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,237 [qtp1566353334-212] INFO  jdbc.sqlonly - SELECT * FROM t_static_curve WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,264 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_auxiliary_line WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,290 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_dynamic_curve WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:52,301 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('b86ab2de-fe84-40b3-945e-2c9facecb2b5','e73efa10-e353-4a08-9ad2-a3a9978c74b4','19939b17-ffa1-4e49-bf6f-7b08487f883a','982aa300-613c-4dfe-a3d9-598da8a20c3d','e8f2731b-1d04-4ba1-97ed-15d8bc05ab07','21ff5e20-3a2e-4deb-b8ea-b87c2267f927') 
; 
 
2025-09-08 10:25:52,302 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '19939b17-ffa1-4e49-bf6f-7b08487f883a' 
; 
 
2025-09-08 10:25:52,303 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,304 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '21ff5e20-3a2e-4deb-b8ea-b87c2267f927' 
; 
 
2025-09-08 10:25:52,304 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,305 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '982aa300-613c-4dfe-a3d9-598da8a20c3d' 
; 
 
2025-09-08 10:25:52,305 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,306 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'b86ab2de-fe84-40b3-945e-2c9facecb2b5' 
; 
 
2025-09-08 10:25:52,306 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,307 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'e73efa10-e353-4a08-9ad2-a3a9978c74b4' 
; 
 
2025-09-08 10:25:52,308 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,309 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'e8f2731b-1d04-4ba1-97ed-15d8bc05ab07' 
; 
 
2025-09-08 10:25:52,309 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,328 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 AND id = 1 LIMIT 1; ; 
 
2025-09-08 10:25:52,355 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group != 0 ORDER BY 
created_time DESC ; 
 
2025-09-08 10:25:52,356 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND disable_flag 
= 0 AND parent_group != '0' AND id != '2' ; 
 
2025-09-08 10:25:52,356 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND parent_group != '0' ; 
 
2025-09-08 10:25:52,358 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT COUNT(*) `total` FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 AND parent_group 
= '0' ; 
 
2025-09-08 10:25:52,359 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT id, name, color, code, status, sample_type, parent_group, disable_flag, created_user_id, 
data, samples FROM t_sample_instance WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:25:52,378 [qtp1566353334-209] INFO  jdbc.sqlonly - SELECT sample_id,sample_name,img,code,created_user_id FROM t_sample WHERE delete_flag = false; 
 
2025-09-08 10:25:52,379 [qtp1566353334-209] INFO  jdbc.sqlonly - SELECT tsp.parameter_id,tsp.parameter_name,tsp.code,tsp.delete_flag,tsp.created_user_id, tsp.sample_id,tsp.order_num,tsp.dimension_id,tsp.units_id,tsp.default_val,tsp.parameter_img, 
tsp.func,tsp.abbreviation,tsp.hidden_flag, tsp.data_type,tsp.select_options,tsp.is_visible_func,tsp.is_disabled_func 
FROM t_sample_parameter tsp WHERE delete_flag = false; 
 
2025-09-08 10:25:52,443 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND type = 'SubTaskVideoRecording' AND action_id 
= '6a579b71-e5fb-4bef-9569-33de699c61ea' ; 
 
2025-09-08 10:25:52,467 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:25:52,468 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE 1=1 AND delete_flag = 0 AND sample_code = 'sample_14785d372' 
; 
 
2025-09-08 10:25:52,475 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT id, name, group_category, code, variable_type, default_val, number_tab, f1_index, is_enable, 
is_feature, is_fx, created_user_id, type, is_overall, contact_code, description, pic, related_result_variable_id, 
related_sub_task_id, default_val, number_tab, reasonable_val_tab, button_tab, button_variable_tab, 
program_tab, text_tab, boolean_tab, select_tab, two_digit_array_tab, control_tab, label_tab, 
picture_tab, related_var_tab, buffer_tab, custom_array_tab, double_array_tab, double_array_list_tab 
FROM t_variable_input WHERE 1=1 AND delete_flag = 0 AND id IN ('2') ORDER BY created_time DESC 
; 
 
2025-09-08 10:25:52,517 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('ff165ef5-aed8-4e76-8d94-2cc1a1ad46bf','1a9fa552-d195-4afd-aa57-d387152d9d3c','f39791e4-c907-4ad7-a5d5-2a01995e53c7','5d38b5ce-9302-460f-884b-7e99ef223f8e') 
; 
 
2025-09-08 10:25:52,518 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '1a9fa552-d195-4afd-aa57-d387152d9d3c' 
; 
 
2025-09-08 10:25:52,519 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,521 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '5d38b5ce-9302-460f-884b-7e99ef223f8e' 
; 
 
2025-09-08 10:25:52,521 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,523 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'f39791e4-c907-4ad7-a5d5-2a01995e53c7' 
; 
 
2025-09-08 10:25:52,524 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,525 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'ff165ef5-aed8-4e76-8d94-2cc1a1ad46bf' 
; 
 
2025-09-08 10:25:52,526 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,543 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('05efd2bd-1fc9-4f07-884e-9e27f838d797','6e103de6-e8b8-4cd4-8d68-8a6ed8edd63a') 
; 
 
2025-09-08 10:25:52,545 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '05efd2bd-1fc9-4f07-884e-9e27f838d797' 
; 
 
2025-09-08 10:25:52,545 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,547 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '6e103de6-e8b8-4cd4-8d68-8a6ed8edd63a' 
; 
 
2025-09-08 10:25:52,547 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,567 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('51390fb6-23ca-4e39-9cc0-38d56c3886a8','54cbdb4f-4b80-4248-a25d-c45d9917bf18','82edce6a-3090-43fd-b6fe-e53086b8dc0d','a2abafd9-d6e1-4342-985c-9dbfc9ed0b6b') 
; 
 
2025-09-08 10:25:52,568 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '51390fb6-23ca-4e39-9cc0-38d56c3886a8' 
; 
 
2025-09-08 10:25:52,569 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,571 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '54cbdb4f-4b80-4248-a25d-c45d9917bf18' 
; 
 
2025-09-08 10:25:52,572 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,573 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '82edce6a-3090-43fd-b6fe-e53086b8dc0d' 
; 
 
2025-09-08 10:25:52,574 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,576 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a2abafd9-d6e1-4342-985c-9dbfc9ed0b6b' 
; 
 
2025-09-08 10:25:52,577 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,594 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('a7be1fc6-0031-4ba0-8e61-3a5509c68f71','2dc40297-0dda-41bb-bfed-5d891fa4ad6e','9430a45d-fd05-4976-bb04-7708f9532661','6060c033-c826-4d7f-9a38-387e6ed6050f') 
; 
 
2025-09-08 10:25:52,595 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '2dc40297-0dda-41bb-bfed-5d891fa4ad6e' 
; 
 
2025-09-08 10:25:52,596 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,597 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '6060c033-c826-4d7f-9a38-387e6ed6050f' 
; 
 
2025-09-08 10:25:52,598 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,600 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '9430a45d-fd05-4976-bb04-7708f9532661' 
; 
 
2025-09-08 10:25:52,600 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,602 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a7be1fc6-0031-4ba0-8e61-3a5509c68f71' 
; 
 
2025-09-08 10:25:52,602 [qtp1566353334-138] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,621 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('8429ab78-098e-45d3-8d0c-3d40ef6dc8be','bde875fc-cea9-425a-8df2-57f7e9754387') 
; 
 
2025-09-08 10:25:52,622 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '8429ab78-098e-45d3-8d0c-3d40ef6dc8be' 
; 
 
2025-09-08 10:25:52,623 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,624 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'bde875fc-cea9-425a-8df2-57f7e9754387' 
; 
 
2025-09-08 10:25:52,626 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:52,646 [qtp1566353334-212] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = '6a579b71-e5fb-4bef-9569-33de699c61ea' LIMIT 
1; ; 
 
2025-09-08 10:25:52,647 [qtp1566353334-212] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = '6a579b71-e5fb-4bef-9569-33de699c61ea' 
; 
 
2025-09-08 10:25:52,650 [qtp1566353334-212] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:25:52,652 [qtp1566353334-212] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【打开项目默认执行动作】') 
; 
 
2025-09-08 10:25:53,408 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('44d5f88b-bd4b-47db-b7fa-cdb029e5cce5','8ac30228-c044-4ed4-8e0e-5f6c249f4229','371d8fe1-292b-4f57-859a-af1f4e5b95c2','a65e2f91-e791-4d41-b5d8-996a160c6f11') 
; 
 
2025-09-08 10:25:53,409 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '371d8fe1-292b-4f57-859a-af1f4e5b95c2' 
; 
 
2025-09-08 10:25:53,409 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:53,410 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '44d5f88b-bd4b-47db-b7fa-cdb029e5cce5' 
; 
 
2025-09-08 10:25:53,411 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:53,412 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '8ac30228-c044-4ed4-8e0e-5f6c249f4229' 
; 
 
2025-09-08 10:25:53,413 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:53,414 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a65e2f91-e791-4d41-b5d8-996a160c6f11' 
; 
 
2025-09-08 10:25:53,414 [qtp1566353334-134] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:25:54,464 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwcxksbs' and delete_flag = false; 
 
2025-09-08 10:25:54,467 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":true,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwcxksbs' and delete_flag 
= false; 
 
2025-09-08 10:25:54,472 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE sample_code = 'sample_14785d372' and variable_code = 
'input_yzlwbs' and delete_flag = false; 
 
2025-09-08 10:25:54,473 [clojure-agent-send-off-pool-0] INFO  jdbc.sqlonly - UPDATE t_sample_variable SET variable_val = '{"value":false,"isConstant":0,"unit":null,"unitType":null,"type":null}' 
WHERE sample_code = 'sample_14785d372' and variable_code = 'input_yzlwbs' and delete_flag = 
false; 
 
2025-09-08 10:25:54,483 [async-dispatch-10] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:25:54,484 [async-dispatch-10] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '结束流程【打开项目默认执行动作】') 
; 
 
2025-09-08 10:27:09,650 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:27:09,653 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 10:27:09,669 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 10:27:09,677 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA cache_size = -16000 
 
2025-09-08 10:27:09,678 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA journal_mode = WAL 
 
2025-09-08 10:27:09,678 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA synchronous = NORMAL 
 
2025-09-08 10:27:09,678 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA temp_store = MEMORY 
 
2025-09-08 10:27:09,678 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA mmap_size = 134217728 
 
2025-09-08 10:27:09,678 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA page_size = 4096 
 
2025-09-08 10:27:09,678 [qtp1566353334-166] INFO  jdbc.sqlonly - PRAGMA auto_vacuum = INCREMENTAL 
 
2025-09-08 10:27:09,686 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('8429ab78-098e-45d3-8d0c-3d40ef6dc8be','bde875fc-cea9-425a-8df2-57f7e9754387') 
; 
 
2025-09-08 10:27:09,687 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '8429ab78-098e-45d3-8d0c-3d40ef6dc8be' 
; 
 
2025-09-08 10:27:09,689 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,690 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'bde875fc-cea9-425a-8df2-57f7e9754387' 
; 
 
2025-09-08 10:27:09,691 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,692 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('44d5f88b-bd4b-47db-b7fa-cdb029e5cce5','8ac30228-c044-4ed4-8e0e-5f6c249f4229','371d8fe1-292b-4f57-859a-af1f4e5b95c2','a65e2f91-e791-4d41-b5d8-996a160c6f11') 
; 
 
2025-09-08 10:27:09,693 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '371d8fe1-292b-4f57-859a-af1f4e5b95c2' 
; 
 
2025-09-08 10:27:09,694 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,695 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '44d5f88b-bd4b-47db-b7fa-cdb029e5cce5' 
; 
 
2025-09-08 10:27:09,696 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,698 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '8ac30228-c044-4ed4-8e0e-5f6c249f4229' 
; 
 
2025-09-08 10:27:09,699 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,700 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a65e2f91-e791-4d41-b5d8-996a160c6f11' 
; 
 
2025-09-08 10:27:09,701 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,719 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('51390fb6-23ca-4e39-9cc0-38d56c3886a8','54cbdb4f-4b80-4248-a25d-c45d9917bf18','82edce6a-3090-43fd-b6fe-e53086b8dc0d','a2abafd9-d6e1-4342-985c-9dbfc9ed0b6b') 
; 
 
2025-09-08 10:27:09,719 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '51390fb6-23ca-4e39-9cc0-38d56c3886a8' 
; 
 
2025-09-08 10:27:09,720 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,722 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '54cbdb4f-4b80-4248-a25d-c45d9917bf18' 
; 
 
2025-09-08 10:27:09,723 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,725 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '82edce6a-3090-43fd-b6fe-e53086b8dc0d' 
; 
 
2025-09-08 10:27:09,726 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,727 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a2abafd9-d6e1-4342-985c-9dbfc9ed0b6b' 
; 
 
2025-09-08 10:27:09,728 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,742 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,744 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:27:09,769 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:27:09,771 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:27:09,788 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('a7be1fc6-0031-4ba0-8e61-3a5509c68f71','2dc40297-0dda-41bb-bfed-5d891fa4ad6e','9430a45d-fd05-4976-bb04-7708f9532661','6060c033-c826-4d7f-9a38-387e6ed6050f') 
; 
 
2025-09-08 10:27:09,789 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '2dc40297-0dda-41bb-bfed-5d891fa4ad6e' 
; 
 
2025-09-08 10:27:09,789 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,791 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '6060c033-c826-4d7f-9a38-387e6ed6050f' 
; 
 
2025-09-08 10:27:09,791 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,792 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '9430a45d-fd05-4976-bb04-7708f9532661' 
; 
 
2025-09-08 10:27:09,793 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,794 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a7be1fc6-0031-4ba0-8e61-3a5509c68f71' 
; 
 
2025-09-08 10:27:09,794 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,821 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('ff165ef5-aed8-4e76-8d94-2cc1a1ad46bf','1a9fa552-d195-4afd-aa57-d387152d9d3c','f39791e4-c907-4ad7-a5d5-2a01995e53c7','5d38b5ce-9302-460f-884b-7e99ef223f8e') 
; 
 
2025-09-08 10:27:09,822 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '1a9fa552-d195-4afd-aa57-d387152d9d3c' 
; 
 
2025-09-08 10:27:09,823 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,826 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '5d38b5ce-9302-460f-884b-7e99ef223f8e' 
; 
 
2025-09-08 10:27:09,827 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,829 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'f39791e4-c907-4ad7-a5d5-2a01995e53c7' 
; 
 
2025-09-08 10:27:09,830 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,833 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'ff165ef5-aed8-4e76-8d94-2cc1a1ad46bf' 
; 
 
2025-09-08 10:27:09,834 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,847 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('b86ab2de-fe84-40b3-945e-2c9facecb2b5','e73efa10-e353-4a08-9ad2-a3a9978c74b4','19939b17-ffa1-4e49-bf6f-7b08487f883a','982aa300-613c-4dfe-a3d9-598da8a20c3d','e8f2731b-1d04-4ba1-97ed-15d8bc05ab07','21ff5e20-3a2e-4deb-b8ea-b87c2267f927') 
; 
 
2025-09-08 10:27:09,849 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '19939b17-ffa1-4e49-bf6f-7b08487f883a' 
; 
 
2025-09-08 10:27:09,850 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,852 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '21ff5e20-3a2e-4deb-b8ea-b87c2267f927' 
; 
 
2025-09-08 10:27:09,853 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,855 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '982aa300-613c-4dfe-a3d9-598da8a20c3d' 
; 
 
2025-09-08 10:27:09,856 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,858 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'b86ab2de-fe84-40b3-945e-2c9facecb2b5' 
; 
 
2025-09-08 10:27:09,859 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,861 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'e73efa10-e353-4a08-9ad2-a3a9978c74b4' 
; 
 
2025-09-08 10:27:09,861 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,863 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'e8f2731b-1d04-4ba1-97ed-15d8bc05ab07' 
; 
 
2025-09-08 10:27:09,864 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,875 [qtp1566353334-214] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:27:09,876 [qtp1566353334-214] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE 1=1 AND delete_flag = 0 AND sample_code = 'sample_14785d372' 
; 
 
2025-09-08 10:27:09,899 [qtp1566353334-214] INFO  jdbc.sqlonly - SELECT id, name, group_category, code, variable_type, default_val, number_tab, f1_index, is_enable, 
is_feature, is_fx, created_user_id, type, is_overall, contact_code, description, pic, related_result_variable_id, 
related_sub_task_id, default_val, number_tab, reasonable_val_tab, button_tab, button_variable_tab, 
program_tab, text_tab, boolean_tab, select_tab, two_digit_array_tab, control_tab, label_tab, 
picture_tab, related_var_tab, buffer_tab, custom_array_tab, double_array_tab, double_array_list_tab 
FROM t_variable_input WHERE 1=1 AND delete_flag = 0 AND id IN ('2') ORDER BY created_time DESC 
; 
 
2025-09-08 10:27:09,945 [qtp1566353334-213] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('05efd2bd-1fc9-4f07-884e-9e27f838d797','6e103de6-e8b8-4cd4-8d68-8a6ed8edd63a') 
; 
 
2025-09-08 10:27:09,947 [qtp1566353334-213] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '05efd2bd-1fc9-4f07-884e-9e27f838d797' 
; 
 
2025-09-08 10:27:09,947 [qtp1566353334-213] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:09,950 [qtp1566353334-213] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '6e103de6-e8b8-4cd4-8d68-8a6ed8edd63a' 
; 
 
2025-09-08 10:27:09,951 [qtp1566353334-213] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,311 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:27:11,312 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:27:11,327 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,329 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:27:11,358 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:27:11,360 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 10:27:11,389 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station_cfg WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,391 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:27:11,416 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:27:11,418 [qtp1566353334-210] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 ; 
 
2025-09-08 10:27:11,434 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('b86ab2de-fe84-40b3-945e-2c9facecb2b5','e73efa10-e353-4a08-9ad2-a3a9978c74b4','19939b17-ffa1-4e49-bf6f-7b08487f883a','982aa300-613c-4dfe-a3d9-598da8a20c3d','e8f2731b-1d04-4ba1-97ed-15d8bc05ab07','21ff5e20-3a2e-4deb-b8ea-b87c2267f927') 
; 
 
2025-09-08 10:27:11,435 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '19939b17-ffa1-4e49-bf6f-7b08487f883a' 
; 
 
2025-09-08 10:27:11,435 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,437 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '21ff5e20-3a2e-4deb-b8ea-b87c2267f927' 
; 
 
2025-09-08 10:27:11,438 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,439 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '982aa300-613c-4dfe-a3d9-598da8a20c3d' 
; 
 
2025-09-08 10:27:11,440 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,441 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'b86ab2de-fe84-40b3-945e-2c9facecb2b5' 
; 
 
2025-09-08 10:27:11,441 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,442 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'e73efa10-e353-4a08-9ad2-a3a9978c74b4' 
; 
 
2025-09-08 10:27:11,443 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,444 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'e8f2731b-1d04-4ba1-97ed-15d8bc05ab07' 
; 
 
2025-09-08 10:27:11,445 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,457 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM ( SELECT *, ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY created_time 
DESC) as row_num FROM t_station_project ) WHERE row_num = 1; 
 
2025-09-08 10:27:11,459 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_station WHERE 1=1 AND delete_flag = 0 AND group_id != '0' ; 
 
2025-09-08 10:27:11,542 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_select_sample WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-08 10:27:11,543 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_sample_variable WHERE 1=1 AND delete_flag = 0 AND sample_code = 'sample_14785d372' 
; 
 
2025-09-08 10:27:11,549 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT id, name, group_category, code, variable_type, default_val, number_tab, f1_index, is_enable, 
is_feature, is_fx, created_user_id, type, is_overall, contact_code, description, pic, related_result_variable_id, 
related_sub_task_id, default_val, number_tab, reasonable_val_tab, button_tab, button_variable_tab, 
program_tab, text_tab, boolean_tab, select_tab, two_digit_array_tab, control_tab, label_tab, 
picture_tab, related_var_tab, buffer_tab, custom_array_tab, double_array_tab, double_array_list_tab 
FROM t_variable_input WHERE 1=1 AND delete_flag = 0 AND id IN ('2') ORDER BY created_time DESC 
; 
 
2025-09-08 10:27:11,677 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('51390fb6-23ca-4e39-9cc0-38d56c3886a8','54cbdb4f-4b80-4248-a25d-c45d9917bf18','82edce6a-3090-43fd-b6fe-e53086b8dc0d','a2abafd9-d6e1-4342-985c-9dbfc9ed0b6b') 
; 
 
2025-09-08 10:27:11,678 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '51390fb6-23ca-4e39-9cc0-38d56c3886a8' 
; 
 
2025-09-08 10:27:11,679 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,680 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '54cbdb4f-4b80-4248-a25d-c45d9917bf18' 
; 
 
2025-09-08 10:27:11,682 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,684 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '82edce6a-3090-43fd-b6fe-e53086b8dc0d' 
; 
 
2025-09-08 10:27:11,685 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,686 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a2abafd9-d6e1-4342-985c-9dbfc9ed0b6b' 
; 
 
2025-09-08 10:27:11,688 [qtp1566353334-215] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,699 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('05efd2bd-1fc9-4f07-884e-9e27f838d797','6e103de6-e8b8-4cd4-8d68-8a6ed8edd63a') 
; 
 
2025-09-08 10:27:11,701 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '05efd2bd-1fc9-4f07-884e-9e27f838d797' 
; 
 
2025-09-08 10:27:11,702 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,703 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '6e103de6-e8b8-4cd4-8d68-8a6ed8edd63a' 
; 
 
2025-09-08 10:27:11,704 [qtp1566353334-135] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,730 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('8429ab78-098e-45d3-8d0c-3d40ef6dc8be','bde875fc-cea9-425a-8df2-57f7e9754387') 
; 
 
2025-09-08 10:27:11,732 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '8429ab78-098e-45d3-8d0c-3d40ef6dc8be' 
; 
 
2025-09-08 10:27:11,733 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,735 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'bde875fc-cea9-425a-8df2-57f7e9754387' 
; 
 
2025-09-08 10:27:11,735 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,751 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('a7be1fc6-0031-4ba0-8e61-3a5509c68f71','2dc40297-0dda-41bb-bfed-5d891fa4ad6e','9430a45d-fd05-4976-bb04-7708f9532661','6060c033-c826-4d7f-9a38-387e6ed6050f') 
; 
 
2025-09-08 10:27:11,753 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '2dc40297-0dda-41bb-bfed-5d891fa4ad6e' 
; 
 
2025-09-08 10:27:11,754 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,755 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '6060c033-c826-4d7f-9a38-387e6ed6050f' 
; 
 
2025-09-08 10:27:11,756 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,758 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '9430a45d-fd05-4976-bb04-7708f9532661' 
; 
 
2025-09-08 10:27:11,759 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,760 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a7be1fc6-0031-4ba0-8e61-3a5509c68f71' 
; 
 
2025-09-08 10:27:11,761 [qtp1566353334-206] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,781 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('ff165ef5-aed8-4e76-8d94-2cc1a1ad46bf','1a9fa552-d195-4afd-aa57-d387152d9d3c','f39791e4-c907-4ad7-a5d5-2a01995e53c7','5d38b5ce-9302-460f-884b-7e99ef223f8e') 
; 
 
2025-09-08 10:27:11,782 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '1a9fa552-d195-4afd-aa57-d387152d9d3c' 
; 
 
2025-09-08 10:27:11,783 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,785 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '5d38b5ce-9302-460f-884b-7e99ef223f8e' 
; 
 
2025-09-08 10:27:11,787 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,788 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'f39791e4-c907-4ad7-a5d5-2a01995e53c7' 
; 
 
2025-09-08 10:27:11,788 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:11,789 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'ff165ef5-aed8-4e76-8d94-2cc1a1ad46bf' 
; 
 
2025-09-08 10:27:11,790 [qtp1566353334-216] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:12,152 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder WHERE 1=1 AND delete_flag = 0 AND binder_id IN ('44d5f88b-bd4b-47db-b7fa-cdb029e5cce5','8ac30228-c044-4ed4-8e0e-5f6c249f4229','371d8fe1-292b-4f57-859a-af1f4e5b95c2','a65e2f91-e791-4d41-b5d8-996a160c6f11') 
; 
 
2025-09-08 10:27:12,153 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '371d8fe1-292b-4f57-859a-af1f4e5b95c2' 
; 
 
2025-09-08 10:27:12,153 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:12,154 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '44d5f88b-bd4b-47db-b7fa-cdb029e5cce5' 
; 
 
2025-09-08 10:27:12,155 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:12,155 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = '8ac30228-c044-4ed4-8e0e-5f6c249f4229' 
; 
 
2025-09-08 10:27:12,155 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 10:27:12,156 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_binder_layout WHERE delete_flag = false AND binder_id = 'a65e2f91-e791-4d41-b5d8-996a160c6f11' 
; 
 
2025-09-08 10:27:12,157 [qtp1566353334-166] INFO  jdbc.sqlonly - SELECT * FROM t_widget WHERE delete_flag = false ; 
 
2025-09-08 11:05:23,307 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1=1 AND delete_flag = 0 AND run_on_stopup = 1 ; 
 
2025-09-08 11:05:23,309 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = 'a5112cfc-f9ee-4232-86e0-ff1e81e9df45' LIMIT 
1; ; 
 
2025-09-08 11:05:23,310 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = 'a5112cfc-f9ee-4232-86e0-ff1e81e9df45' 
; 
 
2025-09-08 11:05:23,312 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 11:05:23,313 [qtp1566353334-205] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【关闭项目时默认执行动作】') 
; 
 
2025-09-08 11:05:23,389 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_action WHERE 1 = 1 AND action_id = '63526397-7d5a-4d20-8840-4fa2ceee596d' LIMIT 
1; ; 
 
2025-09-08 11:05:23,390 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_subtask WHERE delete_flag = false AND action_id = '63526397-7d5a-4d20-8840-4fa2ceee596d' 
; 
 
2025-09-08 11:05:23,391 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT project_name FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
2025-09-08 11:05:23,392 [qtp1566353334-205] INFO  jdbc.sqlonly - INSERT INTO t_log (project_id,project_name,content) VALUES ( 31, 'K1C试验项目0907', '开始流程【关闭项目重置参数】') 
; 
 
2025-09-08 11:05:23,427 [qtp1566353334-205] INFO  jdbc.sqlonly - INSERT INTO t_log (content) VALUES ( '删除了【关闭项目 project_31】项目') ; 
 
2025-09-08 11:05:23,428 [qtp1566353334-205] INFO  jdbc.sqlonly - SELECT * FROM t_project_db WHERE 1 = 1 AND project_id = 31 LIMIT 1; ; 
 
