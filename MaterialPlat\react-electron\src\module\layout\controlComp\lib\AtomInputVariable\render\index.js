import React, { useMemo } from 'react'
import { message } from 'antd'

import { getVariableDefaultInfo } from '@/module/variableInput/constants/variableInfo'
import InputRender from '@/module/variableInput/render/index'
import { updateInputVar } from '@/utils/services'
import { useTrigger } from '@/components/formItems/SetActionOrScript/index'
import useInputVariableValueByCode from '@/hooks/project/inputVariable/useInputVariableValueByCode'
import useInputVariableByCode from '@/hooks/project/inputVariable/useInputVariableByCode'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

const Render = ({
    config: {
        attr: {
            compWidth,
            labelWidth,
            label,
            labelItalic,
            labelBold,
            contentItalic,
            contentBold
        } = {},
        variable: {
            value,
            visible,
            disabled
        } = {},
        event: {
            change
        } = {}
    } = {},
    inputVariableType
}) => {
    const { onEvent } = useTrigger()

    const valueVar = useInputVariableByCode(value?.code, getVariableDefaultInfo({ variable_type: inputVariableType }))

    const applyLabelNameValueVar = useMemo(() => ({ ...valueVar, name: label ?? valueVar.name }), [valueVar, label])

    const isVisible = useInputVariableValueByCode(visible?.code, true)
    const isAbled = useInputVariableValueByCode(disabled?.code, true)

    const onChange = async (v) => {
        if (!value?.code) {
            message.error('未绑定变量')
            return
        }

        // 恢复名称
        const newVari = {
            ...v, name: valueVar.name
        }
        const res = await updateInputVar(newVari)

        if (res) {
            dispatchSyncInputVar({ code: newVari.code }, newVari)
            onEvent(change)
        }
    }

    return (
        <>
            {
                isVisible && (
                    <InputRender
                        variable={applyLabelNameValueVar}
                        disabled={!isAbled}
                        onChange={onChange}
                        labelItalic={labelItalic}
                        labelBold={labelBold}
                        contentItalic={contentItalic}
                        contentBold={contentBold}
                    />
                )
            }
        </>
    )
}

export default Render
