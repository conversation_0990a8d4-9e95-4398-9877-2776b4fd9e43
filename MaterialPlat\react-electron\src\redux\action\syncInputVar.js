import isEqual from 'lodash/isEqual'
import { INPUT_TYPE } from '@/utils/constants'
import { updateInputVar } from '@/utils/services'
import { INPUTVARIABLE_UPDATE } from '@/redux/constants/inputVariable'
import store from '../store/index'

const syncInputVar = ({ code = undefined, ...rest }, data, shouldSyncDB = false) => {
    return (dispatch, getState) => {
        // eslint-disable-next-line prefer-destructuring
        const inputVariableMap = getState().inputVariable.inputVariableMap

        // 发生变化的变量
        const targetVariable = inputVariableMap.get(code)

        if (!targetVariable) {
            console.warn('未找到输入变量, code', code)
            return
        }

        let newV

        if (data) {
            newV = data
        }

        // 增加Object.keys(rest)的判断，因为当rest为{}时还会进入 if判断，导致newV为原来的数据
        if (rest && Object.keys(rest).length > 0) {
            newV = Object.keys(rest).reduce((prev, curr) => {
                if (typeof prev[curr] === 'object') {
                    return { ...prev, [curr]: { ...prev[curr], ...rest[curr] } }
                }
                return { ...prev, [curr]: rest[curr] }
            }, targetVariable)
        }

        // 判断 是否有变化
        if (isEqual(newV, targetVariable)) {
            return
        }

        if (window.isDebug) {
            console.log('修改输入变量,code:', code)
        }

        // 变量-可使用 特殊处理 保存db
        if (
        // 输入变量type不是控件参数
            newV.type === INPUT_TYPE.GENERAL
                            // 需要同步db
                            && shouldSyncDB
                            // 可使用发生改变
                            && newV.is_feature !== targetVariable.is_feature
        ) {
            // 需要调接口更新db
            updateInputVar(newV)
        }

        dispatch({
            type: INPUTVARIABLE_UPDATE,
            param: {
                code,
                newVariable: newV
            }
        })
    }
}

const dispatchSyncInputVar = (...params) => {
    return store.dispatch(syncInputVar(...params))
}

export {
    syncInputVar,
    dispatchSyncInputVar
}
