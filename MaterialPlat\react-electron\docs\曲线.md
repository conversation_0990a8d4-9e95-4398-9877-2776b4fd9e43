## 大纲
1. 曲线的实现
	1. 通用曲线 (ChartXY)
		1. 内部的状态
			- 图表实例引用 (chartRef)
			- X/Y轴映射表 (xAxisMapRef, yAxisMapRef)
			- 曲线映射表 (lineMapRef)
			- 曲线数据缓存 (lineDataMapRef)
			- 十字线状态 (lineCrossMapRef)
			- 标注管理器 (pointTagManagerRef, markerPointMapRef)
			- 辅助线映射 (auxiliaryLinesMapRef)
			- 自动扩展状态 (autoExpandEnabledRef)
		2. 暴露的方法
			- 数据操作: lineAdd, clearLine, clearAllLine
			- 视图控制: restore, executeAutoExpand
			- 十字线: toggleCross, moveCross
			- 标注管理: updatePointMarkers, updateChunkMarkers
			- 辅助线: updateAuxiliaryLines
			- 断裂点: setBreakPoint, clearBreakPoint
		3. 行为
			1. 绘制数据
				- 基于LightningChart JS的高性能渲染
				- 支持多轴多曲线同时显示
				- 实时数据点添加和更新
			2. 自动拓展
				- 根据数据范围智能调整坐标轴
				- 用户手动操作后自动禁用
				- 支持一键恢复默认视图
	2. 业务组件
		1. 组件状态
			- 配置状态 (compConfig): 持久化的组件配置
			- 运行状态 (compStatus): 临时的组件状态
			- 锁定状态 (isLocked): 控制数据更新的锁定机制
			- 标注状态: 点标注、块标注的显示控制
			- 交互状态: 十字线、断裂点、手工标定等临时状态
		2. 状态变化与重置逻辑
			1. 状态重置时机
				- 试验状态变化 (openExperiment): 试验开始/结束时重置所有交互状态
				- 控件配置变化 (initalConfig): 配置更新时重置状态确保一致性
				- 试样切换 (optSample): 切换试样时重置状态避免数据混乱
				- 手工标定重置 (resetMarking): 标定流程变化时重置相关状态
			2. 重置的状态项
				- 十字线状态 (openCross): false
				- 断裂点设置状态 (openBreak): false
				- 断裂点存在状态 (openBreakPoint): false
				- 数据锁定状态 (isLocked): false
				- 手工标定状态: 通过resetMarking()重置
			3. 状态同步机制
				- 点标签显示状态: 与配置中的pointTag.open同步
				- 块标签显示状态: 与配置中的chunkTag.open同步
				- 试验状态下标签强制隐藏: !openExperiment条件控制
			4. 配置状态管理
				- 标签配置更新: 通过setShowPointTag/setShowChunkTag同步到配置
				- 运行状态更新: 通过updateCompStatus更新compStatus
				- 配置持久化: 所有配置变更都会触发updateConfig回调
		3. 老曲线平移
			- convertOldConfigToNew: 配置格式转换
			- 样式映射: 线条样式、比例类型等的兼容转换
			- 标签转换: 点标签、块标签的格式适配
		4. 初始化
			- 配置解析和验证
			- 图表选项构建 (config2ChartOption)
			- 数据源初始化 (initBufferCurve/initArrayCurve)
		5. 实时更新
			- 辅助线动态更新 (useAuxiliaryDynamic)
			- 点标注动态管理 (usePointTagDynamic)
			- 块标注动态管理 (useChunkTagDynamic)
			- 跨线同步输入变量 (useCrossSyncInputVar)
		6. 曲线数据更新
			- 消息订阅机制 (useSubScriberCompMsg)
			- 数据处理和分发 (processLineData)
			- 锁定状态下的数据缓存
	3. 两个曲线控件
		1. 相同点
			- 都基于ChartXY通用曲线组件
			- 相同的配置结构 (base, curveGroup, xAxis, yAxis等)
			- 相同的组态设置界面和交互逻辑
			- 相同的标注和辅助线功能
			- 相同的右键菜单和导出功能
		2. 不同点
			- 数据源类型:
				- CurveDoubleArray: 二维数组数据
				- CurveDaqBuffer: DAQ缓冲区数据
			- 数据初始化方式:
				- CurveDoubleArray: initArrayCurve
				- CurveDaqBuffer: initBufferCurve
			- 试样处理:
				- CurveDoubleArray: 支持多数据源的数组变量
				- CurveDaqBuffer: 基于试样的缓冲区数据
2. 现在的问题以及优化点
	1. 配置再细分控制
		- 配置项过于复杂，需要更细粒度的分组
		- 不同场景下的配置项显示控制
		- 配置验证和默认值处理
	2. 暴露的方法分组维护
		- 数据操作类方法统一管理
		- 视图控制类方法规范化
		- 标注和辅助线方法的一致性
3. 控件通用封装结构
	1. 控件类型 - 按照配置保存位置
		1. 功能控件
			- 配置保存在widget.data_source
			- 包含业务逻辑和数据处理
		2. 原子控件
			- 纯展示组件，无业务逻辑
			- 配置相对简单
	2. 组态弹窗
		- 分步骤的配置界面 (基础配置、曲线配置、高级配置)
		- 配置项的联动和同步机制
		- 配置验证和错误提示
	3. 渲染
		- CompRender: 统一的渲染入口
		- 配置和状态的分离管理
		- 性能优化和内存管理
	4. 右击
		- 上下文菜单的统一管理
		- 功能操作的快捷入口
		- 导出和配置功能集成 不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵

