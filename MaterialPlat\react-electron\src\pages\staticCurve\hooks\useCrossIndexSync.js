import { useState, useEffect } from 'react'
import { updateInputVar } from '@/utils/services'
import useNumberInputVariable from '@/hooks/project/inputVariable/useNumberInputVariable'
import { SELECT_POINT_TYPES } from '@/components/charts/StaticChartLine/constants'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'
import { getProportion } from '../constants'

/**
 * 十字线选中位置和输入变量值同步的自定义 Hook
 * @param {Object} params - 参数对象
 * @param {Object} params.currentState - 当前状态
 * @param {Object} params.sampleHistoryData - 样本历史数据
 * @param {Object} params.optSample - 当前选中的样本
 * @param {string} params.chartPointType - 图表点类型
 * @returns {Object} 返回十字线同步相关的状态和方法
 */
const useCrossIndexSync = ({
    currentState,
    sampleHistoryData,
    optSample,
    chartPointType
}) => {
    const [originIndex, setOriginIndex] = useState(0)
    const [autoIndex, setAutoIndex] = useState(0)

    const inputVariableNumber = useNumberInputVariable()

    /**
     * 处理十字线位置变化，更新输入变量值
     */
    const handelOriginIndex = async () => {
        if (currentState?.settingModalData?.input_code
            && chartPointType === SELECT_POINT_TYPES.cross.key
        ) {
            const input = inputVariableNumber.find(f => f.code === currentState?.settingModalData?.input_code)
            if (input) {
                const data = sampleHistoryData[optSample.code] ?? []
                if (data && data.length > 0) {
                    const startData = data[0]
                    const endData = data[data.length - 1]
                    const targetData = data[originIndex]
                    const proportion = getProportion(startData?.create_time, endData?.create_time, targetData?.create_time) ?? 0
                    if (!Number.isNaN(proportion) && (input.default_val.value ?? 0).toFixed(4) !== Number(proportion).toFixed(4)) {
                        const newInput = {
                            ...input,
                            default_val: {
                                ...input.default_val,
                                value: Number(proportion)
                            }
                        }

                        // 修改redux值不需要防抖
                        dispatchSyncInputVar({ code: input.code }, newInput)

                        // 修改db
                        await updateInputVar(newInput)
                    }
                }
            }
        }
    }

    /**
     * 十字线位置回调函数
     */
    const callBackOriginIndex = async (index) => {
        setOriginIndex(index)
    }

    // 监听十字线index变化
    useEffect(() => {
        handelOriginIndex()
    }, [originIndex])

    // 监听十字线输入变量变化自动设置十字线位置
    useEffect(() => {
        // const input = inputVariableNumber.find(f => f.code === currentState?.settingModalData?.input_code)
        // if (input) {
        //     const data = sampleHistoryData[optSample.code] ?? []
        //     const newIndex = getIndexValue(data, input?.default_val?.value) ?? 0

        //     // 添加一个误差处理 避免小数位算出来不一致的情况
        //     if (Math.abs(newIndex - originIndex) > data.length / 100) {
        //         setAutoIndex(getIndexValue(data, input?.default_val?.value) ?? 0)
        //     }
        // }
    }, [
        currentState?.settingModalData?.input_code,
        inputVariableNumber,
        sampleHistoryData,
        optSample.code,
        originIndex
    ])

    return {
        autoIndex,
        callBackOriginIndex,
        setOriginIndex,
        setAutoIndex
    }
}

export default useCrossIndexSync
