import { getAllSample } from '@/module/layout/controlComp/lib/CurveDaqBuffer/utils/sample'
import { color16 } from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { CURVE_STYLE, SOURCE_TYPE } from './constants'
import { findUnitById } from '../arrayUtils/unit'

const updateCurves = ({
    isBufferCurve, channels, chartCurveGroup, xAxisName, yAxisName, sourceType
}) => {
    const res = {}

    const curves = chartCurveGroup?.curves || {}
    const ySignalArray = chartCurveGroup?.ySignal || []

    Object.keys(curves).forEach((dataSourceKey) => {
        const dataSourceConfig = curves[dataSourceKey]

        let sampleColor
        if (isBufferCurve) {
            const sample = getAllSample().find(s => s.code === dataSourceKey)
            sampleColor = sample?.color
        }

        res[dataSourceKey] = {
            name: dataSourceConfig.name,
            lines: ySignalArray.map((code, index) => {
                const lineId = `${chartCurveGroup.name}-${dataSourceKey}-${code}`
                const xName = channels.find(c => c.code === chartCurveGroup.xSignal)?.name
                const yName = channels.find(c => c.code === code)?.name

                let lineName
                if (isBufferCurve) {
                    if (sourceType === SOURCE_TYPE.单数据源) {
                        lineName = `${yName}-${xName}`
                    } else {
                        lineName = `${dataSourceConfig?.name ?? ''}-${yName}-${xName}`
                    }
                } else {
                    // 2025.9.3 二维数组 修改为 y轴名称/x轴名称
                    lineName = `${yAxisName}-${xAxisName}`
                }

                return {
                    // 先保证默认样式
                    ...CURVE_STYLE,
                    color: color16(),
                    // 试样颜色
                    ...(sampleColor ? { color: sampleColor } : {}),
                    // 再继承之前样式
                    ...(dataSourceConfig?.lines?.find(i => i?.id === lineId) ?? {}),
                    yUnit: channels.find(c => c.code === code)?.unitId,
                    // 更新code
                    code,
                    xName,
                    yName,
                    // 曲线id ： 所在y轴名称-数据源的下标-通道的key
                    id: lineId,
                    // 曲线name 标签使用 ：所在曲线组名称-数据源的名称-通道的名称
                    name: lineName
                }
            })
        }
    })

    return res
}

const handleValuesChange = (changedValues, allValues, form, channels, isBufferCurve) => {
    const newValues = cloneDeep(allValues)

    // 1. x轴信号同步：任一curveGroup的x轴变化都同步到另一个curveGroup
    if (changedValues?.curveGroup?.yAxis?.xSignal) {
        // curveGroup.yAxis的x轴变化，同步到y2Axis
        const newXSignal = changedValues.curveGroup?.yAxis.xSignal

        newValues.curveGroup.y2Axis.xSignal = newXSignal
        newValues.xAxis.unit = channels.find(c => c.code === newXSignal)?.unitId
    }

    if (changedValues?.curveGroup?.y2Axis?.xSignal) {
        // curveGroup.y2Axis的x轴变化，同步到yAxis
        const newXSignal = changedValues.curveGroup?.y2Axis.xSignal
        newValues.curveGroup.yAxis.xSignal = newXSignal
        newValues.xAxis.unit = channels.find(c => c.code === newXSignal)?.unitId
    }

    // x轴信号变化 同步到曲线配置
    if (changedValues?.curveGroup?.yAxis?.xSignal || changedValues?.curveGroup?.y2Axis?.xSignal) {
        const xSignal = changedValues?.curveGroup?.yAxis?.xSignal || changedValues?.curveGroup?.y2Axis?.xSignal

        // 同步修改曲线组的x轴信号
        newValues.curveGroup.yAxis.xSignal = xSignal
        newValues.curveGroup.y2Axis.xSignal = xSignal
    }

    // x轴单温变化
    if (changedValues?.xAxis?.unit) {
        newValues.curveGroup.yAxis.xUnit = changedValues?.xAxis?.unit
        newValues.curveGroup.y2Axis.xUnit = changedValues?.xAxis?.unit
    }

    // 修改x轴名称
    if (changedValues?.curveGroup?.yAxis?.xSignal || changedValues?.curveGroup?.y2Axis?.xSignal || changedValues?.xAxis?.unit) {
        const xSignal = newValues?.curveGroup?.yAxis?.xSignal || newValues?.curveGroup?.y2Axis?.xSignal

        const signalName = channels.find(c => c.code === xSignal)?.name ?? ''
        const signalUnitName = channels?.find(c => c.code === xSignal)?.unitList?.find(i => i.id === newValues?.xAxis?.unit)?.name ?? ''

        // 修改x轴名称 默认通道名称加当前单位
        if (signalUnitName) {
            newValues.xAxis.name = `${signalName}(${signalUnitName})`
        } else {
            newValues.xAxis.name = `${signalName}`
        }
    }

    // 同步曲线
    if (changedValues?.curveGroup?.yAxis?.ySignal || changedValues?.curveGroup?.yAxis?.xSignal) {
        // 更新每个curve组中的code
        const updatedCurves = updateCurves({
            isBufferCurve,
            channels,
            xAxisName: newValues?.xAxis?.name,
            yAxisName: newValues?.yAxis?.name,
            chartCurveGroup: newValues.curveGroup?.yAxis,
            sourceType: newValues?.base?.sourceType
        })

        newValues.curveGroup.yAxis.curves = updatedCurves
    }

    if (changedValues?.curveGroup?.y2Axis?.ySignal || changedValues?.curveGroup?.y2Axis?.xSignal) {
        const updatedCurves = updateCurves({
            isBufferCurve,
            channels,
            xAxisName: newValues?.xAxis?.name,
            yAxisName: newValues?.y2Axis?.name,
            chartCurveGroup: newValues.curveGroup?.y2Axis
        })

        newValues.curveGroup.y2Axis.curves = updatedCurves
    }

    // 设置y轴名称
    if (changedValues?.curveGroup?.yAxis?.ySignal || changedValues?.curveGroup?.yAxis?.curves) {
        // 取第一个数据源的曲线配置中的单位 单数据源都在这里  多数据源单位都一样
        const fristGroupDataSourceConfigLines = Object.values(newValues.curveGroup.yAxis.curves)?.[0].lines

        const getName = (signalName, unitName) => {
            return `${signalName}(${unitName})`
        }

        const y1Name = newValues?.curveGroup?.yAxis?.ySignal.map(i => {
            const line = fristGroupDataSourceConfigLines.find(l => l.code === i)

            const unit = findUnitById(line?.yUnit)
            const signalName = channels.find(c => c.code === i)?.name

            if (unit) {
                return getName(signalName, unit?.name)
            }

            return signalName
        }).join('/')

        newValues.yAxis.name = y1Name
    }

    // y2轴信号变化 修改y2轴名称
    if (changedValues?.curveGroup?.y2Axis?.ySignal || changedValues?.curveGroup?.y2Axis?.curves) {
        // 取第一个数据源的曲线配置中的单位 单数据源都在这里  多数据源单位都一样
        const fristGroupDataSourceConfigLines = Object.values(newValues.curveGroup.y2Axis.curves)?.[0].lines

        const getName = (signalName, unitName) => {
            return `${signalName}(${unitName})`
        }

        const y1Name = newValues?.curveGroup?.y2Axis?.ySignal.map(i => {
            const line = fristGroupDataSourceConfigLines.find(l => l.code === i)

            const unit = findUnitById(line?.yUnit)
            const signalName = channels.find(c => c.code === i)?.name

            if (unit) {
                return getName(signalName, unit?.name)
            }

            return signalName
        }).join('/')

        newValues.y2Axis.name = y1Name
    }

    // 统一设置表单值
    form.setFieldsValue(newValues)
}

export default handleValuesChange

export {
    updateCurves
}
