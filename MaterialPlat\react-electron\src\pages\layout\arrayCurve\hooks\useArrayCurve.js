/* eslint-disable new-cap */
import { useMemo, useCallback } from 'react'
import useCopy from '@/hooks/useCopy'
import { jsPDF } from 'jspdf'
import { COPY_TYPE } from '@/utils/constants'
import { useSelector } from 'react-redux'
import { saveResultData, batchUpdateInputVar } from '@/utils/services'
import { auxiliaryDataType } from '@/pages/dialog/auxiliaryLineModal/components/saveModal/constants'
import useTableConfig from '@/hooks/useTableConfig'
import useProjectHistory from '@/hooks/useProjectHistory'
import useDoubleArrayInputVariable from '@/hooks/project/inputVariable/useDoubleArrayInputVariable'
import useDoubleArrayListInputVariable from '@/hooks/project/inputVariable/useDoubleArrayListInputVariable'
import useNumberInputVariable from '@/hooks/project/inputVariable/useNumberInputVariable'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

import {
    getProcessID, numberFormat, resultFractionalDigit, unitConversion
} from '@/utils/utils'
import { useTranslation } from 'react-i18next'
import useResult from '@/hooks/useResult'
import useStaticCurve from '@/hooks/useStaticCurve'
import { STEP_FORM, SOURCE_TYPE } from '../constants'

const getAxis = (axis, unit) => {
    return {
        gridLine: {
            open: axis.is_grid,
            thickness: axis.grid_thickness,
            color: axis.grid_color,
            lineType: axis.grid_type
        },
        zeroLine: {
            open: axis.is_zero_line,
            thickness: axis.zero_line_thickness,
            color: axis.zero_line_color,
            lineType: axis.zero_line_type
        },
        title: {
            name: axis.name,
            unit: axis.unit,
            title: `${axis.name}${unit?.name ? `(${unit?.name})` : ''}`
        },
        axis: {
            thickness: axis.thickness,
            lineType: axis.type,
            color: axis.color
        },
        interval: {
            proportionType: axis.proportion_type,
            start: axis.low_limit,
            end: axis.up_limit,
            intervalType: axis.interval_type,
            intervalNumber: axis.interval_number,
            lastRange: axis.lastRange
        }
    }
}

export const getStyles = (styles) => {
    return styles.map(m => {
        return {
            code: m.code,
            line: {
                open: m.is_line,
                color: m.color,
                lineType: m.line_type,
                thickness: m.line_thickness
            },
            sign: {
                open: m.is_sign,
                color: m.color,
                each: m.sign_each,
                signType: m.sign_type
            }
        }
    })
}

const useArrayCurve = () => {
    const { t } = useTranslation()

    // 选中的曲线
    const optSample = useSelector(state => state.project.optSample)
    const resultHistoryData = useSelector(state => state.project.resultHistoryData)
    const resultData = useSelector(state => state.template.resultData)
    const resultTestData = useSelector(state => state.template.resultTestData)
    const unitList = useSelector(state => state.global.unitList)

    const { copy } = useCopy()
    const { getCurveResults } = useResult()
    const { getAuxiliaryLines } = useStaticCurve()
    const { initTableConfigData } = useTableConfig()
    const { initProjectHistoryData } = useProjectHistory()
    const { initResultData } = useResult()

    const inputVariableNumber = useNumberInputVariable()
    const inputVariableDoubleArray = useDoubleArrayInputVariable()
    const inputVariableDoubleArrayList = useDoubleArrayListInputVariable()

    const getAxleSource = useCallback((option) => {
        if (option.base.source_type === SOURCE_TYPE.二维数组) {
            const selectedDoubleArrayVariable = inputVariableDoubleArray.find(f => f.code === option.base.source_input_code)
            return {
                number: 1,
                columns: selectedDoubleArrayVariable?.double_array_tab?.columns ?? []
            }
        }

        if (option.base.source_type === SOURCE_TYPE.二维数组集合) {
            const doubleArrayList = inputVariableDoubleArrayList.find(f => f.code === option.base.source_input_code)
            const selectedDoubleArrayVariable = inputVariableDoubleArray.find(f => f.code === doubleArrayList?.double_array_list_tab.dataSourceCode)
            return {
                number: doubleArrayList?.double_array_list_tab.number ?? 1,
                columns: selectedDoubleArrayVariable?.double_array_tab?.columns ?? []
            }
        }
        return {
            number: 1,
            columns: []
        }
    }, [inputVariableDoubleArray,
        inputVariableDoubleArrayList])

    // 转换曲线配置
    const convertCurveOption = (option) => {
        const axisUnit = convertAxisUnit(option)
        const {
            base: {
                name,
                is_name
            },
            x_axis,
            y_axis,
            y2_axis,
            curve
        } = option
        return {
            chart: {
                title: is_name ? name : ''
            },
            line: [],
            xAxis: [getAxis(x_axis, axisUnit.x_axis_unit)],
            yAxis: curve[1].is_enable
                ? [getAxis(y_axis, axisUnit.y_axis_unit), getAxis(y2_axis, axisUnit.y2_axis_unit)]
                : [getAxis(y_axis, axisUnit.y_axis_unit)]
        }
    }

    const copyClipboard = (domId) => {
        const dom = document.getElementById(domId)
        const domCanvas = dom.querySelector('canvas')
        domCanvas.toBlob(blob => {
            copy(blob, COPY_TYPE.图片)
        })
    }

    // 打印曲线图
    const printCurve = (domId) => {
        const dom = document.getElementById(domId)
        const domCanvas = dom.querySelector('canvas')
        console.dir(domCanvas)
        const pdf = new jsPDF({ orientation: 'l' })
        pdf.addImage(domCanvas.toDataURL('image/jpg'), 'PNG', 0, 0, pdf.internal.pageSize.getWidth(), pdf.internal.pageSize.getHeight())
        pdf.save('curve.pdf')
    }

    const originData2ChartData = (data, proportion) => {
        if (proportion === undefined) {
            return data
        }

        return data * proportion
    }

    // 负责提取某列数据
    const getColumnValues = (data, key) => {
        const values = []
        for (let i = 0; i < data.length; i += 1) {
            values.push(data[i]?.[key]?.value)
        }
        return values
    }

    //  处理单个 y 轴的数据点。
    const processYAxis = (xData, yData, createTimeData, axisUnit, axisKey) => {
        const val = []
        for (let i = 0; i < xData.length; i += 1) {
            const xValue = xData[i]
            const yValue = yData[i]
            if (Number.isFinite(xValue) && Number.isFinite(yValue)) {
                val.push({
                    x: originData2ChartData(xValue, axisUnit.x_axis_unit?.proportion || 1),
                    y: originData2ChartData(yValue, axisUnit[axisKey]?.proportion || 1),
                    create_time: createTimeData[i]
                })
            }
        }
        return val
    }
    // processCurve 处理单个曲线。
    const processCurve = (curve, data, axisUnit, type) => {
        const newCurveAxis = []
        const xData = getColumnValues(data, curve.x_axis)
        const createTimeData = getColumnValues(data, 'create_time')

        for (let i = 0; i < curve.y_axis.length; i += 1) {
            const yData = getColumnValues(data, curve.y_axis[i])
            const axisKey = curve.index === 0 ? 'y_axis_unit' : 'y2_axis_unit'
            newCurveAxis.push({ type, data: processYAxis(xData, yData, createTimeData, axisUnit, axisKey) })
        }
        return newCurveAxis
    }

    const convertCurveData = (curveAxisArr = [], doubleArray, axisUnit) => {
        const newDoubleArray = []
        const { type } = doubleArray
        for (let i = 0; i < curveAxisArr.length; i += 1) {
            const curveAxis = curveAxisArr[i]
            if (!doubleArray?.data?.[i]) {
                newDoubleArray.push(null)
                // eslint-disable-next-line no-continue
                continue
            }
            const data = doubleArray?.data?.[i] ?? [] // 获取当前曲线轴对应的数据
            for (let j = 0; j < curveAxis.length; j += 1) {
                newDoubleArray.push(processCurve(curveAxis[j], data, axisUnit, type))
            }
        }
        return newDoubleArray
    }

    const convertCurveLineStyles = useCallback((option = {}) => {
        const { curve: curves = [] } = option
        const { number = 1 } = getAxleSource(option)
        const enableCurves = curves.filter((curve) => curve.is_enable) // 筛选出启用的曲线
        return Array.from({ length: number }, (_, index) => {
            return enableCurves
                .map((curve) => ({
                    ...curve,
                    styles: getStyles(curve.styles[index] ?? []) // 提取第一个样式
                }))
        })
    }, [getAxleSource])

    const createPointRes = async ({
        index,
        point,
        variableName,
        variableCode,
        xSignal,
        ySignal
    }) => {
        try {
            const resParam = [{
                variable_name: `${variableName}_${xSignal.variable_name}`,
                dimension_id: xSignal.dimension_id, // 关联量纲id
                unit_id: xSignal.unit_id, // 单位id
                variable_code: `${variableCode}_${xSignal.variable_name}`, // 结果变量的code 格式: `result_${signal.variable_name}_${用户输入code}`,
                value: point.x, // 结果变量的值
                index, // 该试样上的index
                sample_instance_code: optSample.code // 关联的试样code
            },
            {
                variable_name: `${variableName}_${ySignal.variable_name}`, // 结果变量名称 格式: `${用户输入名称}_${signal.variable_name}`,
                dimension_id: ySignal.dimension_id,
                unit_id: ySignal.unit_id,
                variable_code: `${variableCode}_${ySignal.variable_name}`,
                value: point.y,
                index,
                sample_instance_code: optSample.code
            }]
            await saveResultData({ results: resParam })
            initTableConfigData()
            initProjectHistoryData()
            initResultData()
        } catch (error) {
            console.error('Error in createPointRes:', error)
        }
    }
    const convertAxisUnit = (option) => {
        const {
            curve, x_axis, y_axis, y2_axis
        } = option
        const axleSource = getAxleSource(option)
        const xDimensionId = axleSource.columns.find(col => col.code === curve[0].x_axis)?.typeParam?.dimensionId
        const yDimensionId = axleSource.columns.find(col => col.code === curve[0]?.y_axis?.[0])?.typeParam?.dimensionId
        const y2DimensionId = axleSource.columns.find(col => col.code === curve[1]?.y_axis?.[0])?.typeParam?.dimensionId

        return {
            x_axis_unit: unitList.find(f => f.id === xDimensionId)?.units?.find(unit => unit.code === x_axis.unit),
            y_axis_unit: unitList.find(f => f.id === yDimensionId)?.units?.find(unit => unit.code === y_axis.unit),
            y2_axis_unit: unitList.find(f => f.id === y2DimensionId)?.units?.find(unit => unit.code === y2_axis.unit)
        }
    }

    const currentSampleResultHistoryData = useMemo(() => {
        const resultsVarsList = getCurveResults()
        const tags = resultHistoryData[optSample.code]?.map(tag => {
            const originResultData = resultsVarsList.find(item => item.code === tag.code)
            return {
                ...originResultData,
                ...tag,
                value: numberFormat(tag.format_type, tag.value === '--' ? 0 : unitConversion(tag.value, tag.dimension_id, tag.unit_id),
                    resultFractionalDigit(tag.format_type, tag.format_info))
            }
        })
        return tags
    }, [resultData,
        resultTestData,
        optSample
    ])

    // 校验是否可以手工标记
    const verifyResultMarking = (code) => {
        const result = resultData.find(f => f.code === code)
        if (result) {
            if (!(result?.marking_flag ?? false)) {
                return Promise.reject(new Error(t('当前结果变量，未开启手工标记')))
            }
            if (!(result?.marking_count > 0)) {
                return Promise.reject(new Error(t('当前结果变量，标记点不能小于0')))
            }
        } else {
            return Promise.reject(new Error(t('未找到结果变量')))
        }
        return Promise.resolve(result)
    }

    const setMarkingStepInput = async (option, value, step) => {
        // 修改输入变量参数
        if (option?.marker) {
            const marking = option?.marker?.[step]
            if (marking) {
                const input = inputVariableNumber.find(f => f.code === marking?.input_code)
                if (input) {
                    subBatchInputVar(input, value)
                } else {
                    return Promise.reject(new Error(`${t('未找到第')}${step}${t('步的变量')}`))
                }
            }
        }
        return Promise.resolve()
    }

    const subBatchInputVar = async (input, value) => {
        await batchUpdateInputVar({
            input_vars: [{
                id: input.id,
                default_val: {
                    ...input.default_val,
                    value: Number(value)
                }
            }]
        })
        dispatchSyncInputVar({
            code: input.code,
            default_val: {
                value: Number(value)
            }
        })
    }

    const convertTags = (option) => {
        if (option.tag.is_tag) {
            return option.tag.tags.map(tag => {
                return {
                    ...tag,
                    results: tag.results.map(result => {
                        const currentResultHistoryData = currentSampleResultHistoryData
                            ?.find(f => f?.result_variable_id === result?.result_variable_id)
                        if (result?.is_sample && result?.sample_code === optSample?.code) {
                            return null
                        }
                        if (currentResultHistoryData) {
                            return {
                                ...result,
                                ...currentResultHistoryData
                            }
                        }
                        return null
                    }).filter(Boolean)
                }
            })
        }
        return []
    }

    // 曲线的辅助线数据
    const curveAuxiliaryLineData = async (option = {}, auxiliaryLine) => {
        const { auxiliary, tag: { is_tag, is_chunk_tag }, curve } = option
        const lineTagShow = is_tag || is_chunk_tag

        if (!lineTagShow) return [] // 提前返回空数组

        // 处理辅助线ID的去重，提取成一个函数
        const getAuxiliaryIds = (auxiliaryParam, resultAuxiliary) => {
            return [...new Set([
                ...auxiliaryParam,
                ...resultAuxiliary.flatMap(m => m?.auxiliary_line_arr)
            ])]
        }

        // 获取当前启用曲线的y轴
        const getYAxis = (curves) => {
            const enabledCurves = curves.filter(c => c.is_enable)
            return enabledCurves.flatMap(m => m.y_axis)
        }

        // 获取启用曲线的x轴
        const getXAxis = (curves) => {
            const enabledCurves = curves.filter(c => c.is_enable)
            return enabledCurves[0]?.x_axis
        }

        // 获取历史数据中的信号变量
        const resultAuxiliary = currentSampleResultHistoryData?.filter(f => f.auxiliary_line_flag && f.channel_type === auxiliaryDataType.信号变量) ?? []

        // 获取去重后的辅助线ID
        const ids = getAuxiliaryIds(auxiliary, resultAuxiliary)

        // 获取曲线的x轴和y轴
        const yAxis = getYAxis(curve)
        const xAxis = getXAxis(curve)

        // 筛选出符合条件的辅助线
        const auxiliaryLines = auxiliaryLine.filter(f => xAxis === f.x_channel && yAxis.includes(f.y_channel) && ids.includes(f.id))

        // 请求辅助线数据
        const res = await getAuxiliaryLines({ lines: auxiliaryLines })
        return res
    }

    return {
        convertCurveOption,
        copyClipboard,
        printCurve,
        convertCurveData,
        convertCurveLineStyles,
        createPointRes,
        getAxleSource,
        subBatchInputVar,
        convertAxisUnit,
        currentSampleResultHistoryData,
        convertTags,
        verifyResultMarking,
        setMarkingStepInput,
        curveAuxiliaryLineData
    }
}

export default useArrayCurve
