/* eslint-disable no-param-reassign */
import { useCallback } from 'react'
import { useDispatch } from 'react-redux'

import { SCRIPT_TYPE } from '@/utils/constants'
import store from '@/redux/store'
import { dataKey } from '@/pages/dialog/inputVariableManage/varModal/tabProgram/constant'
import { TEMPLATE_VARIABLES } from '@/redux/constants/template'
import { callInputVar, subtaskBatchScript, updateInputVar } from '@/utils/services'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

const scriptMappings = {
    isVisible: SCRIPT_TYPE.BOOL,
    unit: SCRIPT_TYPE.STRING,
    isDisabled: SCRIPT_TYPE.BOOL,
    mode: SCRIPT_TYPE.STRING,
    isCheck: SCRIPT_TYPE.BOOL,
    isCheckDisabled: SCRIPT_TYPE.BOOL,
    // 这个不在这里触发 只会在用户修改时触发
    // onChange: SCRIPT_TYPE.BOOL,
    onExceedMax: SCRIPT_TYPE.BOOL,
    onExceedMin: SCRIPT_TYPE.BOOL
}

const useInputVariables = () => {
    const dispatch = useDispatch()

    // 修改变量值
    const updateInputVariableValue = useCallback(async ({ code, value }) => {
        const v = store?.getState()?.inputVariable.inputVariableMap.get(code)
        if (!v) {
            return
        }

        const newVariable = {
            ...v,
            default_val: {
                ...v?.default_val,
                value
            }
        }

        await updateInputVar(newVariable)
    }, [])

    // 修改db和redux的变量值
    const updateInputVariableValueDB = useCallback(async ({ code, value, shouldAsync = true }) => {
        const v = store?.getState()?.inputVariable.inputVariableMap.get(code)

        if (!v) {
            return
        }

        const newVariable = {
            ...v,
            default_val: {
                ...v?.default_val,
                value
            }
        }

        // 更新db
        if (shouldAsync) {
            await updateInputVar(newVariable)
        } else {
            updateInputVar(newVariable)
        }

        // 更新redux
        dispatchSyncInputVar({ code }, newVariable)
    }, [updateInputVariableValue])

    const correlationVariables = async (param) => {
        dispatch({
            type: TEMPLATE_VARIABLES,
            param
        })
    }

    // 普通输入变量调用脚本
    const subCorrelationVariables = async (param = [], subTaskId, scheduler, isAction) => {
        const getScript = (script, type = SCRIPT_TYPE.BOOL) => {
            return {
                script,
                'result-type': type,
                subTaskId,
                is_action: isAction
            }
        }
        const data = param.map(({ code, program_tab = {} }) => {
            const temp = Object.entries(scriptMappings).reduce((acc, [key, type]) => {
                if (
                    program_tab[key]
                    && ![dataKey.onChange, dataKey.onExceedMax, dataKey.onExceedMin].includes(program_tab[key])
                ) {
                    acc[key] = getScript(program_tab[key], type)
                }
                return acc
            }, {})

            return Object.keys(temp).length ? { [code]: temp } : null
        }).filter(Boolean)
        try {
            if (data && data.length > 0) {
                const res = await callInputVar({ scripts: data, action_context: scheduler?.scheduler_context })
                if (res) {
                    return res
                }
            }
        } catch (error) {
            console.log(error)
        }
        return null
    }

    // 子任务参数用的调用脚本
    const subTaskCorrelationVariables = async (param = [], subTaskId, action_id) => {
        const getScript = (script, type = SCRIPT_TYPE.BOOL) => {
            return {
                script,
                'result-type': type,
                subtask_id: subTaskId,
                action_id
            }
        }
        const data = param.map(({ code, program_tab = {} }) => {
            const temp = Object.entries(scriptMappings).reduce((acc, [key, type]) => {
                if (program_tab[key]) {
                    acc[key] = getScript(program_tab[key], type)
                }
                return acc
            }, {})

            return Object.keys(temp).length ? { [code]: temp } : null
        }).filter(Boolean)

        try {
            if (data && data.length > 0) {
                const res = await subtaskBatchScript({ scripts: data })
                if (res) {
                    return res
                }
            }
        } catch (error) {
            console.log(error)
        }
        return null
    }

    return {
        updateInputVariableValueDB,
        updateInputVariableValue,
        correlationVariables,
        subCorrelationVariables,
        subTaskCorrelationVariables
    }
}

export default useInputVariables
