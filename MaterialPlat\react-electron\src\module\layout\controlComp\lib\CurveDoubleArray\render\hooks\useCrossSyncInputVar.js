import { useMemo, useCallback } from 'react'
import debounce from 'lodash/debounce'

import useInputVariableByCode from '@/hooks/project/inputVariable/useInputVariableByCode'
import { updateInputVar } from '@/utils/services'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

/**
 * 十字线同步输入变量的自定义Hook
 * 用于处理图表十字线位置与输入变量的双向同步
 *
 * @param {Object} config - 配置对象
 * @param {Object} config.base - 基础配置
 * @param {string} config.base.crossInputCode - 输入变量的代码标识
 *
 * @returns {Object} 返回对象
 * @returns {number|null} returns.crossPercent - 当前十字线位置的百分比值(0-1)
 * @returns {Function} returns.onCrossMove - 十字线移动时的回调函数
 */
const useCrossSyncInputVar = ({ config }) => {
    // 根据配置获取对应的输入变量
    const crossPercentVariable = useInputVariableByCode(config?.base?.crossInputCode)

    /**
     * 计算当前十字线位置的百分比
     * 从输入变量中获取值并进行有效性验证
     */
    const crossPercent = useMemo(() => {
        // 如果没有对应的输入变量，返回null
        if (!crossPercentVariable) {
            return null
        }

        // 验证百分比值是否在有效范围内(0-1)
        if (crossPercentVariable?.default_val?.value < 0 || crossPercentVariable?.default_val?.value > 1) {
            return null
        }

        // 返回百分比值，默认为0
        return crossPercentVariable?.default_val?.value || 0
    }, [crossPercentVariable])

    /**
     * 防抖的十字线移动处理函数
     * 使用debounce避免频繁的API调用和状态更新
     * 延迟300ms执行，在用户快速移动十字线时只执行最后一次操作
     */
    const debouncedOnCrossMove = useCallback(
        debounce(async ({
            lineId, // 线条ID
            lineLength, // 线条总长度
            pointIndex // 当前点的索引
        }) => {
            // 如果没有对应的输入变量，直接返回
            if (!crossPercentVariable) {
                return
            }

            // 计算新的百分比值：当前点索引 / 总长度
            const newPercent = pointIndex / lineLength

            // 构造新的变量对象，更新百分比值
            const newVari = {
                ...crossPercentVariable,
                default_val: {
                    ...crossPercentVariable.default_val,
                    value: newPercent
                }
            }

            // 同步更新Redux状态
            dispatchSyncInputVar({ code: newVari.code }, newVari)
            // 同步更新后台数据
            await updateInputVar(newVari)
        }, 300), // 300ms防抖延迟
        [updateInputVar, crossPercentVariable]
    )

    /**
     * 十字线移动回调函数
     * 当用户移动十字线时触发，调用防抖处理函数
     *
     * @param {Object} params - 参数对象
     * @param {string} params.lineId - 线条ID
     * @param {number} params.lineLength - 线条总长度
     * @param {number} params.pointIndex - 当前点的索引
     */
    const onCrossMove = useCallback(({
        lineId,
        lineLength,
        pointIndex
    }) => {
        // 调用防抖函数，传入所有必要的参数
        debouncedOnCrossMove({
            lineId,
            lineLength,
            pointIndex
        })
    }, [debouncedOnCrossMove, crossPercentVariable])

    return {
        crossPercent, // 当前十字线位置的百分比
        onCrossMove // 十字线移动回调函数
    }
}

export default useCrossSyncInputVar
