/* eslint-disable no-param-reassign */
import { KEYBOARD_KEYS } from '../constants/index'

// 统一设置十字线和结果标签
const setCrossPoint = ({
    point,
    line,
    lineCross,
    resultLabel: {
        titleElement,
        pointIndexText,
        pointXText,
        pointYText
    },
    lineCrossIndexRef
}) => {
    // 十字线移动
    lineCross.setPosition(point)

    // 设置结果标签内容
    titleElement.setText(`十字线(${line?.getName()})`)
    pointIndexText.setText(`索引: ${point.index}`)
    pointXText.setText(`${line?.xName || line.axisX.getTitle()}: ${point?.x}`)
    pointYText.setText(`${line?.yName || line.axisY.getTitle()}: ${point?.y}`)

    // 同步十字线当前点的下标
    lineCrossIndexRef.current = point.index
}

// 移动十字线 -------------------------------------------------------------------------------------------------
const step = {
    [KEYBOARD_KEYS.移动十字线.右移一个]: 1,
    [KEYBOARD_KEYS.移动十字线.右移十个]: 10,
    [KEYBOARD_KEYS.移动十字线.左移一个]: -1,
    [KEYBOARD_KEYS.移动十字线.左移十个]: -10
}

const moveCross = ({
    key,
    lineData,
    line,
    lineCross,
    resultLabel,
    lineCrossIndexRef,
    onCrossMoveRef,
    breakPointIndex
}) => {
    let targetIndex = lineCrossIndexRef.current + step[key]

    // 边界处理
    let maxIndex = lineData.length - 1

    // 如果设置了断裂点，限制十字线只能在断裂点之前移动
    if (breakPointIndex !== undefined && breakPointIndex >= 0) {
        maxIndex = Math.min(maxIndex, breakPointIndex)
    }

    if (targetIndex > maxIndex) {
        targetIndex = maxIndex
    } else if (targetIndex < 0) {
        targetIndex = 0
    }

    const point = lineData[targetIndex]

    if (!point) {
        return
    }

    onCrossMoveRef.current({
        lineId: line.id,
        lineLength: lineData.length - 1,
        pointIndex: point.index
    })

    setCrossPoint({
        point,
        line,
        lineCross,
        resultLabel,
        lineCrossIndexRef
    })
}

// 为每一条线添加点击同步十字线 -----------------------------------------------------------------------------------------
const lineClickSyncCross = ({
    lineMap,
    lineCrossMap,
    resultLabel,
    lineCrossIndexRef,
    lineDataMapRef,
    onCrossMoveRef
}) => {
    Object.keys(lineMap).forEach((id) => {
        const line = lineMap[id]
        const lineCross = lineCrossMap[id]

        line.onMouseClick((token, e) => {
            // 判断当前十字线是否开启
            if (!lineCross.getVisible()) {
                return
            }

            const { x, y } = token.chart.engine.clientLocation2Engine(e.clientX, e.clientY)
            const point = token.solveNearestFromScreen({ x, y }).location

            onCrossMoveRef.current({
                lineId: line.id,
                lineLength: lineDataMapRef.current[line.id].length - 1,
                pointIndex: point.index
            })

            setCrossPoint({
                point,
                line,
                lineCross,
                resultLabel,
                lineCrossIndexRef
            })
        })
    })
}

// 切换线 -------------------------------------------------------------------------------------------------
// ctrl+left 的监听 分成两个 ctrlKey：true 和 key：ArrowLeft 这里直接根据方向键
const indexStep = {
    ArrowLeft: -1,
    ArrowRight: 1
}

const crossSwitchLine = ({
    key,
    lineCrossMap,
    lineCrossIdRef,
    lineCrossIndexRef,
    resultLabel,
    lineMap,
    lineDataMap,
    breakPointRef,
    onHighlightChange
}) => {
    const lineIds = Object.keys(lineCrossMap)

    // 一共一条线不切换
    if (lineIds.length === 1) {
        return
    }

    // 当前线下标
    const currentIndex = lineIds.findIndex(i => i === lineCrossIdRef.current)

    // 目标线下标
    let targetLineIndex = currentIndex + indexStep[key]
    // 边界处理
    if (targetLineIndex < 0) {
        targetLineIndex = lineIds.length - 1
    } else if (targetLineIndex > lineIds.length - 1) {
        targetLineIndex = 0
    }

    const targetLineId = lineIds[targetLineIndex]

    // 开启目标线的十字线 并 默认选中最后一个点
    openCross({
        targetLineId,
        lineCrossIdRef,
        lineCrossIndexRef,
        lineCrossMap,
        resultLabel,
        lineMap,
        lineDataMap,
        breakPointRef,
        onHighlightChange
    })
}

// 开启/切换十字线 并选中目标线的最后一个点 ---------------------------------------------------------------------------------
const openCross = ({
    targetLineId,
    lineCrossIdRef,
    lineCrossIndexRef,
    lineCrossMap,
    resultLabel,
    lineMap,
    lineDataMap,
    breakPointRef,
    onHighlightChange
}) => {
    // 记录旧的线ID用于高亮切换
    const oldLineId = lineCrossIdRef.current

    // 关闭之前十字线
    if (lineCrossIdRef.current) {
        lineCrossMap[lineCrossIdRef.current]?.setVisible(false)
    }

    // 开启新的十字线
    const newLineCross = lineCrossMap[targetLineId]
    newLineCross?.setVisible(true)
    lineCrossIdRef.current = targetLineId

    // 调用高亮切换回调
    if (onHighlightChange && typeof onHighlightChange === 'function') {
        onHighlightChange(targetLineId, oldLineId)
    }

    // 显示结果标签
    resultLabel.layout.setVisible(true)

    // 设置目标点（考虑断裂点）
    const line = lineMap[targetLineId]
    const lineData = lineDataMap?.[targetLineId]

    if (!lineData || lineData.length === 0) {
        return
    }

    // 如果有断裂点，使用断裂点位置，否则使用最后一个点
    const breakPointIndex = breakPointRef?.current?.[targetLineId]
    let targetPoint
    let targetIndex

    if (breakPointIndex !== undefined && breakPointIndex !== null && breakPointIndex < lineData.length) {
        targetPoint = lineData[breakPointIndex]
        targetIndex = breakPointIndex
    } else {
        targetPoint = lineData.at(-1)
        targetIndex = lineData.length - 1
    }

    lineCrossIndexRef.current = targetIndex

    setCrossPoint({
        point: targetPoint,
        line,
        lineCross: newLineCross,
        resultLabel,
        lineCrossIndexRef
    })
}

// 根据百分比移动十字线 ---------------------------------------------------------------------------------
const moveCrossByPercent = ({
    crossPercent,
    lineCrossIdRef,
    lineDataMapRef,
    lineMapRef,
    lineCrossMapRef,
    resultLabelRef,
    lineCrossIndexRef,
    breakPointRef
}) => {
    // 如果没有十字线开启或者crossPercent无效，则不处理
    if (!lineCrossIdRef.current || crossPercent === undefined || crossPercent === null) {
        return
    }

    const currentLineId = lineCrossIdRef.current
    const lineData = lineDataMapRef.current?.[currentLineId]
    const line = lineMapRef.current?.[currentLineId]
    const lineCross = lineCrossMapRef.current?.[currentLineId]

    if (!lineData || !line || !lineCross || lineData.length === 0) {
        return
    }

    // 计算目标索引位置（基于百分比）
    const maxIndex = lineData.length - 1

    let targetIndex = Math.round(crossPercent * maxIndex)

    // 如果设置了断裂点，限制十字线只能在断裂点之前移动
    const breakPointIndex = breakPointRef?.current?.[currentLineId]
    if (breakPointIndex !== undefined && breakPointIndex >= 0) {
        targetIndex = Math.min(targetIndex, breakPointIndex)
    }

    // 获取当前十字线位置的百分比
    const currentPercent = lineCrossIndexRef.current / maxIndex

    // 精度判断：如果当前位置和目标位置的百分比差异小于阈值，则不移动
    // 阈值设为1%，避免频繁的微小移动
    const threshold = 0.01
    if (Math.abs(currentPercent - crossPercent) < threshold) {
        console.log('不移动')
        return
    }

    // 边界处理
    const safeTargetIndex = Math.max(0, Math.min(targetIndex, maxIndex))
    const targetPoint = lineData[safeTargetIndex]

    if (!targetPoint) {
        return
    }

    // 使用统一的setCrossPoint函数移动十字线
    setCrossPoint({
        point: targetPoint,
        line,
        lineCross,
        resultLabel: resultLabelRef.current,
        lineCrossIndexRef
    })
}

export {
    setCrossPoint,
    moveCross,
    lineClickSyncCross,
    crossSwitchLine,
    openCross,
    moveCrossByPercent
}
