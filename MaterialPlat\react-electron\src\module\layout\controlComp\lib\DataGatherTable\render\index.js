import React, { useMemo } from 'react'

import { updateInputVar } from '@/utils/services'
import useInputVariableByCode from '@/hooks/project/inputVariable/useInputVariableByCode'
import { dispatchSyncInputVar } from '@/redux/action/syncInputVar'

import { TableContainer } from './style'
import Table2DataGather from './table2DataGather/index'
import TableHorizontal from './tableHorizontal'

const RenderTable2DataGather = ({
    config: {
        attr: {
            isHorizontal,
            dataColumnsConfig
        } = {},
        variable: {
            value
        } = {}
    } = {}
}) => {
    const valueVari = useInputVariableByCode(value?.code)

    const onChange = async (v) => {
        const res = await updateInputVar(v)

        if (res) {
            dispatchSyncInputVar({ code: v.code }, v)
        }
    }

    return (
        <TableContainer>
            {
                isHorizontal
                    ? (
                        // 横向
                        <TableHorizontal
                            config={dataColumnsConfig}
                            value={valueVari?.default_val?.value}
                        />
                    ) : (
                        // 纵向
                        <Table2DataGather
                            dataColumnsConfig={dataColumnsConfig}
                            variable={valueVari}
                            handleUpdateVariable={(newVari) => onChange(newVari)}
                        />

                    )
            }
        </TableContainer>
    )
}

export default RenderTable2DataGather
