/* eslint-disable no-restricted-syntax */
/* eslint-disable prefer-destructuring */
/**
 * 辅助线工具函数
 * 用于计算和管理辅助线的位置和样式
 */

import {
    ColorRGBA, SolidLine, SolidFill, DashedLine, ColorCSS
} from '@arction/lcjs'

/**
 * 获取辅助线样式
 * @param {Object} style - 样式配置对象
 * @param {string} style.color - 线条颜色
 * @param {number} style.thickness - 线条粗细
 * @param {string} style.pattern - 线条样式（'solid', 'dashed', 'dotted'）
 * @returns {SolidLine|DashedLine} 线条样式对象
 */
export const getAuxiliaryLineStyle = (style) => {
    const color = ColorCSS(style.line_color)
    const thickness = style.thickness || 2

    if (style.pattern === 'dashed' || style.line_type === 'dashed') {
        return new DashedLine({
            thickness,
            fillStyle: new SolidFill({ color }),
            patternScale: 3
        })
    }

    if (style.pattern === 'dotted' || style.line_type === 'dotted') {
        return new DashedLine({
            thickness,
            fillStyle: new SolidFill({ color }),
            patternScale: 1
        })
    }

    return new SolidLine({
        thickness,
        fillStyle: new SolidFill({ color })
    })
}

/**
 * 获取当前轴范围
 * @param {Object} xAxisMap - X轴映射表
 * @param {Object} yAxisMap - Y轴映射表
 * @param {string} [xAxisId] - 指定的X轴ID，如果不指定则使用第一个X轴
 * @param {string} [yAxisId] - 指定的Y轴ID，如果不指定则使用第一个Y轴
 * @returns {Object} 轴范围对象
 */
export const getAxisRanges = (xAxisMap, yAxisMap, xAxisId, yAxisId) => {
    const xAxisKeys = Object.keys(xAxisMap)
    const yAxisKeys = Object.keys(yAxisMap)

    if (xAxisKeys.length === 0 || yAxisKeys.length === 0) {
        return {
            xMin: 0, xMax: 100, yMin: 0, yMax: 100
        }
    }

    // 根据指定的轴ID获取对应的轴，如果没有指定或找不到则使用第一个轴
    let targetXAxis
    if (xAxisId && xAxisMap[xAxisId]) {
        targetXAxis = xAxisMap[xAxisId]
    } else {
        targetXAxis = xAxisMap[xAxisKeys[0]]
    }

    let targetYAxis
    if (yAxisId && yAxisMap[yAxisId]) {
        targetYAxis = yAxisMap[yAxisId]
    } else {
        targetYAxis = yAxisMap[yAxisKeys[0]]
    }

    const xInterval = targetXAxis.getInterval()
    const yInterval = targetYAxis.getInterval()

    return {
        xMin: xInterval.start,
        xMax: xInterval.end,
        yMin: yInterval.start,
        yMax: yInterval.end
    }
}

/**
 * 根据下标获取线上的数据点
 * @param {Array} lineData - 线条数据数组
 * @param {number} index - 数据点下标
 * @returns {Object|null} 数据点对象或null
 */
export const getPointByIndex = (lineData, index) => {
    if (!lineData || !Array.isArray(lineData)) {
        return null
    }
    return lineData.find(point => point.index === index) || null
}

/**
 * 计算直线的两个端点（基于点和斜率）
 * @param {Object} point - 直线上的一个点 {x, y}
 * @param {number} slope - 直线斜率
 * @param {Object} axisRanges - 轴范围
 * @returns {Object} 包含起点和终点的对象
 */
export const calculateLinePoints = (point, slope, axisRanges) => {
    const {
        xMin, xMax, yMin, yMax
    } = axisRanges

    const b = point.y - slope * point.x
    const intersections = []

    // 与左边界的交点
    const yAtXMin = slope * xMin + b
    if (yAtXMin >= yMin && yAtXMin <= yMax) {
        intersections.push({ x: xMin, y: yAtXMin })
    }

    // 与右边界的交点
    const yAtXMax = slope * xMax + b
    if (yAtXMax >= yMin && yAtXMax <= yMax) {
        intersections.push({ x: xMax, y: yAtXMax })
    }

    // 与下边界的交点
    if (slope !== 0) {
        const xAtYMin = (yMin - b) / slope
        if (xAtYMin >= xMin && xAtYMin <= xMax) {
            intersections.push({ x: xAtYMin, y: yMin })
        }
    }

    // 与上边界的交点
    if (slope !== 0) {
        const xAtYMax = (yMax - b) / slope
        if (xAtYMax >= xMin && xAtYMax <= xMax) {
            intersections.push({ x: xAtYMax, y: yMax })
        }
    }

    // 如果斜率为0（水平线）
    if (slope === 0) {
        if (point.y >= yMin && point.y <= yMax) {
            intersections.push({ x: xMin, y: point.y })
            intersections.push({ x: xMax, y: point.y })
        }
    }

    // 去重
    const uniqueIntersections = intersections.filter((pt, index, arr) => index === arr.findIndex(p => Math.abs(p.x - pt.x) < 0.001 && Math.abs(p.y - pt.y) < 0.001))

    if (uniqueIntersections.length >= 2) {
        return {
            start: uniqueIntersections[0],
            end: uniqueIntersections[uniqueIntersections.length - 1]
        }
    }

    return {
        start: { x: xMin, y: slope * xMin + b },
        end: { x: xMax, y: slope * xMax + b }
    }
}

/**
 * 计算垂直线的端点
 * @param {number} x - 垂直线的X坐标
 * @param {Object} axisRanges - 轴范围
 * @returns {Object} 包含起点和终点的对象
 */
export const calculateVerticalLinePoints = (x, axisRanges) => {
    const { yMin, yMax } = axisRanges
    return {
        start: { x, y: yMin },
        end: { x, y: yMax }
    }
}

/**
 * 根据配置计算线条端点
 * 支持多种配置类型：
 * - segment: 两点配置（支持坐标点和下标点的任意组合）
 * - slopeStraight: 一点+斜率配置（支持坐标点和下标点）
 * - xStraight: 垂直X轴配置
 *
 * 点类型判断：
 * - 如果点包含 index 属性，则为下标点，需要从 lineDataMap 中获取实际坐标
 * - 如果点包含 x, y 属性，则为坐标点，直接使用坐标值
 * - 当任一点为下标点时，需要指定 lineId 以从 lineDataMap 中获取数据
 *
 * @param {Object} config - 辅助线配置
 * @param {string} config.type - 辅助线类型（'straight' | 'segment'）
 * @param {string} config.configType - 配置类型（'segment' | 'slopeStraight' | 'xStraight'）
 * @param {string} [config.lineId] - 线条ID（用于下标配置）
 * @param {Array} config.data - 配置数据数组
 * @param {Object} axisRanges - 轴范围
 * @param {Object} lineDataMap - 线条数据映射表（用于下标配置）
 * @param {Object} lineMap - 线条映射表
 * @returns {Object} 包含起点和终点的对象 {startPoint, endPoint}
 */
export const calculatePointsFromConfig = (config, axisRanges, lineDataMap = {}, lineMap = {}) => {
    let startPoint
    let endPoint

    // 获取点的实际坐标（支持下标点和坐标点）
    const getActualPoint = (pointData) => {
        if (pointData.index !== undefined) {
            // 下标点：从线条数据中获取坐标
            const lineData = lineDataMap[config.lineId]
            if (!lineData) {
                return null
            }
            return getPointByIndex(lineData, pointData.index)
        } if (pointData.x !== undefined && pointData.y !== undefined) {
            // 坐标点：直接使用 x, y 坐标
            return { x: pointData.x, y: pointData.y }
        }
        return null
    }

    if (config.type === 'straight') {
        if (config.configType === 'segment') {
            // 两点配置 - 直线
            const point1 = getActualPoint(config.data[0])
            const point2 = getActualPoint(config.data[1])

            if (!point1 || !point2) {
                return { startPoint: null, endPoint: null }
            }

            const slope = (point2.y - point1.y) / (point2.x - point1.x)
            const linePoints = calculateLinePoints(point1, slope, axisRanges)
            startPoint = linePoints.start
            endPoint = linePoints.end
        } else if (config.configType === 'slopeStraight') {
            // 斜率配置 - 直线
            const pointData = config.data[0]
            const actualPoint = getActualPoint(pointData)

            if (!actualPoint) {
                return { startPoint: null, endPoint: null }
            }

            const slope = pointData.slope || pointData.m // 兼容旧的m字段
            const points = calculateLinePoints(actualPoint, slope, axisRanges)
            startPoint = points.start
            endPoint = points.end
        } else if (config.configType === 'xStraight') {
            // 垂直X轴配置 - 直线
            const { x } = config.data[0]
            const points = calculateVerticalLinePoints(x, axisRanges)
            startPoint = points.start
            endPoint = points.end
        }
    } else if (config.type === 'segment') {
        if (config.configType === 'segment') {
            // 两点配置 - 线段
            const point1 = getActualPoint(config.data[0])
            const point2 = getActualPoint(config.data[1])

            if (!point1 || !point2) {
                return { startPoint: null, endPoint: null }
            }

            startPoint = point1
            endPoint = point2
        }
    }

    return { startPoint, endPoint }
}

/**
 * 更新单个辅助线位置（当轴范围变化时或配置更新时）
 * @param {string} auxiliaryLineId - 辅助线ID
 * @param {Object} auxiliaryLinesMap - 辅助线映射表
 * @param {Object} xAxisMap - X轴映射表
 * @param {Object} yAxisMap - Y轴映射表
 * @param {Object} lineDataMap - 线条数据映射表（用于下标配置）
 * @param {Object} lineMap - 线条映射表
 * @param {Object} [newConfig] - 新的配置（可选，用于更新配置）
 */
export const updateSingleAuxiliaryLine = (auxiliaryLineId, auxiliaryLinesMap, xAxisMap, yAxisMap, lineDataMap = {}, lineMap = {}, newConfig = null) => {
    const auxiliaryLineData = auxiliaryLinesMap[auxiliaryLineId]
    if (!auxiliaryLineData || !auxiliaryLineData.line) {
        console.warn(`辅助线 ${auxiliaryLineId} 不存在`)
        return false
    }

    // 如果提供了新配置，则更新配置
    if (newConfig) {
        auxiliaryLineData.config = { ...auxiliaryLineData.config, ...newConfig }

        // 如果样式发生变化，更新线条样式
        if (newConfig.style) {
            const lineStyle = getAuxiliaryLineStyle(newConfig.style)
            auxiliaryLineData.line.setStrokeStyle(lineStyle)
        }
    }

    const { line, config } = auxiliaryLineData

    // 根据配置中的轴ID获取对应的轴范围
    const axisRanges = getAxisRanges(xAxisMap, yAxisMap, config.xAxisId, config.yAxisId)
    const { startPoint, endPoint } = calculatePointsFromConfig(config, axisRanges, lineDataMap, lineMap)

    if (startPoint && endPoint) {
        line.clear()
        line.add([startPoint, endPoint])
        return true
    }

    return false
}

/**
 * 更新辅助线位置（当轴范围变化时）
 * @param {Object} auxiliaryLineData - 辅助线数据
 * @param {Object} xAxisMap - X轴映射表
 * @param {Object} yAxisMap - Y轴映射表
 * @param {Object} lineDataMap - 线条数据映射表（用于下标配置）
 */
export const updateAuxiliaryLine = (auxiliaryLineData, xAxisMap, yAxisMap, lineDataMap = {}) => {
    const { line, config } = auxiliaryLineData
    if (!line) return

    // 根据配置中的轴ID获取对应的轴范围
    const axisRanges = getAxisRanges(xAxisMap, yAxisMap, config.xAxisId, config.yAxisId)
    const { startPoint, endPoint } = calculatePointsFromConfig(config, axisRanges, lineDataMap)

    if (startPoint && endPoint) {
        line.clear()
        line.add([startPoint, endPoint])
    }
}

/**
 * 更新所有直线类型的辅助线
 * @param {Object} auxiliaryLinesMap - 辅助线映射表
 * @param {Object} xAxisMap - X轴映射表
 * @param {Object} yAxisMap - Y轴映射表
 * @param {Object} lineDataMap - 线条数据映射表（用于下标配置）
 */
export const updateAllAuxiliaryLines = (auxiliaryLinesMap, xAxisMap, yAxisMap, lineDataMap = {}) => {
    Object.values(auxiliaryLinesMap).forEach(auxiliaryLineData => {
        updateAuxiliaryLine(auxiliaryLineData, xAxisMap, yAxisMap, lineDataMap)
    })
}

/**
 * 绘制单个辅助线
 * @param {Object} auxiliaryLineData - 辅助线数据
 * @param {Object} xAxisMap - X轴映射表
 * @param {Object} yAxisMap - Y轴映射表
 * @param {Object} lineDataMap - 线条数据映射表（用于下标配置）
 */
export const drawAuxiliaryLine = (auxiliaryLineData, xAxisMap, yAxisMap, lineDataMap = {}) => {
    const { line, config } = auxiliaryLineData
    if (!line) return

    // 应用线条样式
    if (config.style) {
        const lineStyle = getAuxiliaryLineStyle(config.style)
        line.setStrokeStyle(lineStyle)
    }

    // 根据配置中的轴ID获取对应的轴范围
    const axisRanges = getAxisRanges(xAxisMap, yAxisMap, config.xAxisId, config.yAxisId)
    const { startPoint, endPoint } = calculatePointsFromConfig(config, axisRanges, lineDataMap)
    if (startPoint && endPoint) {
        line.clear()
        line.add([startPoint, endPoint])
    }
}

/**
 * 绘制所有辅助线
 * @param {Object} auxiliaryLinesMap - 辅助线映射表
 * @param {Object} xAxisMap - X轴映射表
 * @param {Object} yAxisMap - Y轴映射表
 * @param {Object} lineDataMap - 线条数据映射表（用于下标配置）
 */
export const drawAllAuxiliaryLines = (auxiliaryLinesMap, xAxisMap, yAxisMap, lineDataMap = {}) => {
    Object.values(auxiliaryLinesMap).forEach(auxiliaryLineData => {
        drawAuxiliaryLine(auxiliaryLineData, xAxisMap, yAxisMap, lineDataMap)
    })
}
