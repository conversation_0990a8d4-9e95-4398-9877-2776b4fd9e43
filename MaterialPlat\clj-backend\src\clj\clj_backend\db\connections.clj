(ns clj-backend.db.connections
  (:require
   [clj-backend.common.biz-error :refer [errors throw-error]]
   [clj-backend.common.db-utils :as db-utils]
   [clj-backend.db.core :as db-core]
   [mount.core :refer [defstate]]
   [next.jdbc :as jdbc]
   [taoensso.timbre :refer [warn]]))

(def template-table-name "t_template_db")
(def template-id-name "template_id")
(def project-table-name "t_project_db")
(def project-id-name "project_id")

(defstate ^:dynamic *db-connections
  :start    (atom {})
  :stop     (when *db-connections
              (run! #(.close %) (vals @*db-connections))))

(defn- connecte-db
  "连接并记录db，增加性能优化配置"
  [process-id db-path]
  (warn "[连接监控] 开始创建数据库连接 -" "连接ID:" process-id "数据库路径:" db-path)
  (let [start-time (System/currentTimeMillis)
        conn (jdbc/get-connection {:jdbcUrl (format "jdbc:log4jdbc:sqlite:%s" db-path)})]
    (warn "[连接监控] 数据库连接创建成功 -" "连接ID:" process-id "耗时:" (- (System/currentTimeMillis) start-time) "ms")
    ;; 执行性能优化 PRAGMA 语句，特别针对机械硬盘优化
    (try
      (warn "[连接监控] 开始应用性能优化配置 -" "连接ID:" process-id)
      ;; 增大缓存提升写入性能 (16MB)
      (jdbc/execute! conn ["PRAGMA cache_size = -16000"])
      ;; 使用 WAL 模式提高并发写入性能
      (jdbc/execute! conn ["PRAGMA journal_mode = WAL"])
      ;; 平衡性能和安全性，适合机械硬盘
      (jdbc/execute! conn ["PRAGMA synchronous = NORMAL"])
      ;; 临时表和索引存储在内存中
      (jdbc/execute! conn ["PRAGMA temp_store = MEMORY"])
      ;; 启用内存映射 (128MB)
      (jdbc/execute! conn ["PRAGMA mmap_size = 134217728"])
      ;; 优化页面大小，适合机械硬盘
      (jdbc/execute! conn ["PRAGMA page_size = 4096"])
      ;; 增量自动清理，减少碎片
      (jdbc/execute! conn ["PRAGMA auto_vacuum = INCREMENTAL"])
      (warn "[连接监控] 性能优化配置应用成功 -" "连接ID:" process-id)
      (catch Exception e
        (warn "[连接监控] 应用 SQLite 性能优化配置时出错 -" "连接ID:" process-id "错误:" (.getMessage e))))
    (swap! *db-connections assoc process-id conn)
    (warn "[连接监控] 连接已加入连接池 -" "连接ID:" process-id "当前连接池大小:" (count @*db-connections))
    conn))

(defn- get-template-db-conn
  "获取模板库连接"
  [template-id]
  (warn "[连接监控] 请求获取模板库连接 -" "模板ID:" template-id)
  (when-not template-id (throw-error (:NullPointer errors) template-id))
  (let [conn-key (str "template_" template-id)]
    (if-let [conn (@*db-connections conn-key)]
      (do
        (warn "[连接监控] 复用现有模板库连接 -" "连接ID:" conn-key "当前连接池大小:" (count @*db-connections))
        conn)
      (let [template (db-utils/find-detail
                      {:table-name template-table-name
                       :id-name    template-id-name
                       :id         template-id})]
        (when-not template (throw-error (:NullPointer errors) template-id))
        (warn "[连接监控] 创建新的模板库连接 -" "连接ID:" conn-key "模板目录:" (:template_directory template))
        (connecte-db
         conn-key
         (str (:template_directory template) "/"
              (:template_prefix template) "_template_" template-id ".db"))))))

(defn- get-project-db-conn
  "获取项目库连接, 并在项目库中附加数据库"
  [project-id]
  (warn "[连接监控] 请求获取项目库连接 -" "项目ID:" project-id)
  (when-not project-id (throw-error (:NullPointer errors) project-id))
  (let [conn-key (str "project_" project-id)]
    (if-let [conn (@*db-connections conn-key)]
      (do
        (warn "[连接监控] 复用现有项目库连接 -" "连接ID:" conn-key "当前连接池大小:" (count @*db-connections))
        conn)
      (let [project (db-utils/find-detail
                     {:table-name project-table-name
                      :id-name    project-id-name
                      :id         project-id})
            _       (when-not project (throw-error (:NullPointer errors) project-id))
            conn    (do
                      (warn "[连接监控] 创建新的项目库连接 -" "连接ID:" conn-key "项目目录:" (:project_directory project))
                      (connecte-db
                       conn-key
                       (str (:project_directory project) "/"
                            (:project_prefix project) "_project_" project-id ".db")))]
        ;; 附加项目数据库
        #_(db-utils/attach-project-data-db
           conn
           {:db-path (str (:project_directory project)"/"
                          (:project_prefix project) "_project_data_" project-id ".db")})
        (warn "[连接监控] 项目库连接创建完成 -" "连接ID:" conn-key)
        conn))))

(def get-conn-lock (Object.))

(defn get-db-conn
  "获取数据库连接"
  ([] db-core/*db*)
  ([{:keys [template_id project_id] :as _template-sgin}]
   (warn "[连接监控] 请求获取数据库连接 -" "模板ID:" template_id "项目ID:" project_id)
   (locking get-conn-lock
     (let [start-time (System/currentTimeMillis)
           result (-> (cond
                        ;; 获取模板库连接
                        template_id (get-template-db-conn template_id)
                        ;; 获取项目库连接
                        project_id  (get-project-db-conn project_id)
                        ;; 获取系统数据库
                        :else       db-core/*db*))
           end-time (System/currentTimeMillis)
           duration (- end-time start-time)]
       (warn "[连接监控] 数据库连接获取完成 -" "模板ID:" template_id "项目ID:" project_id "耗时:" duration "ms" "当前连接池大小:" (count @*db-connections))
       result))))

(defn try-close-db-conn
  [{:keys [template_id project_id]}]
  (let [conn-key (cond template_id (str "template_" template_id)
                       project_id  (str "project_" project_id)
                       :else       nil)
        conn     (@*db-connections conn-key)]
    (warn "[连接监控] 请求关闭数据库连接 -" "连接ID:" conn-key "关闭前连接池大小:" (count @*db-connections))
    (when conn
      (let [start-time (System/currentTimeMillis)]
        (try
          ;; 在关闭前尝试最后一次WAL检查点，确保数据持久化
          (warn "[连接监控] 开始执行WAL检查点 -" "连接ID:" conn-key)
          (jdbc/execute! conn ["PRAGMA wal_checkpoint(PASSIVE)"])
          (warn "[连接监控] WAL检查点执行完成 -" "连接ID:" conn-key)
          (Thread/sleep 50)
          ;; 关闭连接
          (warn "[连接监控] 开始关闭数据库连接 -" "连接ID:" conn-key)
          (.close conn)
          (swap! *db-connections dissoc conn-key)
          (let [end-time (System/currentTimeMillis)
                duration (- end-time start-time)]
            (warn "[连接监控] 连接关闭完成 -" "连接ID:" conn-key "耗时:" duration "ms" "关闭后连接池大小:" (count @*db-connections)))
          (catch Exception e
            (let [end-time (System/currentTimeMillis)
                  duration (- end-time start-time)]
              (warn "[连接监控] 关闭" conn-key "数据库连接时出错:" (.getMessage e) "耗时:" duration "ms"))
            ;; 即使WAL检查点失败，也要尝试关闭连接
            (try
              (warn "[连接监控] 尝试强制关闭连接 -" "连接ID:" conn-key)
              (.close conn)
              (swap! *db-connections dissoc conn-key)
              (warn "[连接监控] 强制关闭连接成功 -" "连接ID:" conn-key)
              (catch Exception close-e
                (warn "[连接监控] 强制关闭" conn-key "连接失败:" (.getMessage close-e))))))))))


(defn get-project-data-db-conn
  "获取项目data数据库连接"
  [{:keys [project_id]}]
  (let [start-time (System/currentTimeMillis)
        conn-key (str "project_data_" project_id)]
    (warn "[连接监控] 请求获取项目数据连接 -" "项目ID:" project_id "连接ID:" conn-key "当前连接池大小:" (count @*db-connections))
    (if-let [conn (@*db-connections conn-key)]
      (do
        (let [end-time (System/currentTimeMillis)
              duration (- end-time start-time)]
          (warn "[连接监控] 复用现有项目数据连接 -" "项目ID:" project_id "连接ID:" conn-key "耗时:" duration "ms"))
        conn)
      (let [project-info (db-utils/find-detail
                          {:table-name project-table-name
                           :id-name    project-id-name
                           :id         project_id})
            db-path      (str (:project_directory project-info)"/"
                              (:project_prefix project-info) "_project_data_" project_id ".db")
            conn         (connecte-db conn-key db-path)
            end-time     (System/currentTimeMillis)
            duration     (- end-time start-time)]
        (warn "[连接监控] 创建新项目数据连接完成 -" "项目ID:" project_id "连接ID:" conn-key "数据库路径:" db-path "耗时:" duration "ms" "连接池大小:" (count @*db-connections))
        conn))))

(defn try-close-project-data-db-conn
  "关闭项目data数据库连接"
  [{:keys [project_id]}]
  (let [conn-key (str "project_data_" project_id)
        conn     (@*db-connections conn-key)]
    (warn "[连接监控] 请求关闭项目数据连接 -" "项目ID:" project_id "连接ID:" conn-key "关闭前连接池大小:" (count @*db-connections))
    (when conn
      (let [start-time (System/currentTimeMillis)]
        (try
          ;; 在关闭前尝试最后一次WAL检查点，确保数据持久化
          (warn "[连接监控] 开始执行项目数据WAL检查点 -" "项目ID:" project_id "连接ID:" conn-key)
          (jdbc/execute! conn ["PRAGMA wal_checkpoint(PASSIVE)"])
          (warn "[连接监控] 项目数据WAL检查点执行完成 -" "项目ID:" project_id "连接ID:" conn-key)
          (Thread/sleep 50)
          ;; 关闭连接
          (warn "[连接监控] 开始关闭项目数据连接 -" "项目ID:" project_id "连接ID:" conn-key)
          (.close conn)
          (swap! *db-connections dissoc conn-key)
          (let [end-time (System/currentTimeMillis)
                duration (- end-time start-time)]
            (warn "[连接监控] 项目数据连接关闭完成 -" "项目ID:" project_id "连接ID:" conn-key "耗时:" duration "ms" "关闭后连接池大小:" (count @*db-connections)))
          (catch Exception e
            (let [end-time (System/currentTimeMillis)
                  duration (- end-time start-time)]
              (warn "[连接监控] 关闭项目数据连接时出错 -" "项目ID:" project_id "连接ID:" conn-key "错误:" (.getMessage e) "耗时:" duration "ms"))
            ;; 即使WAL检查点失败，也要尝试关闭连接
            (try
              (warn "[连接监控] 尝试强制关闭项目数据连接 -" "项目ID:" project_id "连接ID:" conn-key)
              (.close conn)
              (swap! *db-connections dissoc conn-key)
              (warn "[连接监控] 强制关闭项目数据连接成功 -" "项目ID:" project_id "连接ID:" conn-key)
              (catch Exception close-e
                (warn "[连接监控] 强制关闭项目数据连接失败 -" "项目ID:" project_id "连接ID:" conn-key "错误:" (.getMessage close-e))))))))))
