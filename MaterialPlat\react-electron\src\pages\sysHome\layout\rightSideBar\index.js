/* eslint-disable react/jsx-one-expression-per-line */
import React, { useState, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Link, useHistory } from 'react-router-dom'
import { Select, message } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import { getRolesList, getUserInfo } from '@/utils/auth'
import { ROUTERS } from '@/routers/mainRouter'
import useDialog from '@/hooks/useDialog'
import useHelpDoc from '@/hooks/useHelpDoc'
import {
    DIALOG_UNIT,
    DIALOG_SAMPLE,
    DIALOG_AUDIO,
    DIALOG_PICTURE,
    DIALOG_HARDWARE,
    DIALOG_TOTAL_STATION,
    DIALOG_HARDWARE_CONFIG,
    DIALOG_LANG,
    DIALOG_SPOT_CHECK,
    DIALOG_USER,
    DIALOG_ROL<PERSON>,
    DIALOG_EDIT_PASSWORD,
    DIALOG_SYSTEM_LOG_MODAL,
    DIALOG_CONTROL_LIBRARY,
    DIALOG_SYSTEM_CONFIG_MODAL,
    DIALOG_MASTER_SLAVE_MANAGER,
    DIALOG_APP_MANAGER
} from '@/redux/constants/dialog'
import {
    getLanguages,
    getInternationalization
} from '@/utils/services'

import {
    homeIconAdminOpt,
    homeIconAdmin,
    homeIconChat,
    homeIconHelp,
    homeIconLanguage,
    homeIconSetting,
    homeIconSystem,
    homeIconChatOpt,
    homeIconHelpOpt,
    homeIconLanguageOpt,
    homeIconSettingOpt,
    homeIconSystemOpt
} from '@/static/img'

import { GLOBAL_SET_LANG } from '@/redux/constants/global'

import { SPLIT_PDF_CONTROL } from '@/redux/constants/split'
import { PANEL_TYPE } from './constants'
import { RightSideBarContainer } from './style'

const RightSideBar = () => {
    const role = getRolesList()
    const { t, i18n } = useTranslation()
    const { openDialog } = useDialog()
    const isLangOpen = useSelector(state => state.dialog.isLangOpen)
    const userIsAdmin = useSelector(state => state.global.userIsAdmin)
    const { openHelpDocByMdTag } = useHelpDoc()

    const [showIcon, setShowIcon] = useState(false)
    const [actionKey, setActionKey] = useState()
    const lang = useSelector(state => state.global.lang)
    const [select, setSelect] = useState([])
    const ref2Container = useRef()

    const dispatch = useDispatch()

    useEffect(() => {
        if (showIcon) {
            document.addEventListener('click', handleCheckCLick)
        }
        return () => {
            if (showIcon) {
                document.removeEventListener('click', handleCheckCLick)
            }
        }
    }, [showIcon])

    const handleCheckCLick = (e) => {
        if (!e.path.includes(ref2Container.current)) {
            handleDrawerChangeClose()
        }
    }

    const handleDrawerChangeClose = () => {
        setShowIcon(false)
        setActionKey('')
    }

    useEffect(() => {
        if (getUserInfo()) {
            getLanguagesInfo()
        }
    }, [isLangOpen])

    const getLanguagesInfo = async () => {
        try {
            const res = await getLanguages()

            if (res) {
                setSelect([
                    { value: 'zh', label: '中文' },
                    ...res.map((i) => ({ value: i, label: i }))
                ])
                getInternationalizationInfo(
                    localStorage.getItem('lang') || 'zh'
                )
            }
        } catch (error) {
            console.log(error)
            throw error
        }
    }

    const handleIcon = (type) => {
        setShowIcon(true)
        if (actionKey === type) {
            setActionKey('')
            return
        }
        setTimeout(() => {
            setActionKey(type)
        }, 100)
    }

    const getInternationalizationInfo = async (val) => {
        localStorage.setItem('lang', val)
        dispatch({ type: GLOBAL_SET_LANG, param: val })

        if (val === 'zh') {
            setSelectI18n(val, {})
            return
        }
        try {
            const res = await getInternationalization({ language_name: val })
            if (res) {
                setSelectI18n(val, res)
            }
        } catch (error) {
            console.log(error)
            throw error
        }
    }

    // 切换语言
    const setSelectI18n = (val, info) => {
        i18n.addResources(val, 'translation', info)
        i18n.changeLanguage(val)
    }

    return (
        <RightSideBarContainer ref={ref2Container} showIcon={showIcon}>
            {/* 用户 */}
            {
                role?.侧边栏?.用户 && (
                    <div className="icons-icon-layout">
                        <div
                            id="user-icon"
                            className="right-icons-icon"
                            onClick={() => handleIcon(PANEL_TYPE.USER)}
                        >
                            <img src={actionKey === PANEL_TYPE.USER ? homeIconAdminOpt : homeIconAdmin} alt="" className="icon-img" />
                            <div className={[`header-user ${actionKey === PANEL_TYPE.USER ? 'weight' : ''}`]}>
                                {t('用户')}
                            </div>
                        </div>
                        <div className="panel-container">
                            {
                                role?.侧边栏?.用户?.切换用户?.visible && (
                                    <Link to={ROUTERS.切换用户.path}>
                                        <div className={[`user ${actionKey === PANEL_TYPE.USER ? 'hide' : ''}`]}>
                                            <div id="switch-user">
                                                {t('切换用户') }
                                            </div>
                                        </div>
                                    </Link>
                                )
                            }
                            {
                                role?.侧边栏?.用户?.用户列表?.用户列表页?.visible && !!userIsAdmin && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.USER ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_USER })}
                                    >
                                        <div>
                                            {t('用户列表')}
                                        </div>
                                    </div>
                                )
                            }
                            {/* { */}
                            {/*    role?.侧边栏?.用户?.角色?.visible && ( */}
                            {/*        <div */}
                            {/*            className={[`user ${actionKey === PANEL_TYPE.USER ? 'hide' : ''}`]} */}
                            {/*            onClick={() => openDialog({ type: DIALOG_ROLE })} */}
                            {/*        > */}
                            {/*            <div> */}
                            {/*                {t('角色')} */}
                            {/*            </div> */}
                            {/*        </div> */}
                            {/*    ) */}
                            {/* } */}
                            {
                                role?.侧边栏?.用户?.修改密码?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.USER ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_EDIT_PASSWORD })}
                                    >
                                        <div>
                                            {t('修改密码')}
                                        </div>
                                    </div>
                                )
                            }
                        </div>
                    </div>
                )
            }
            {/* 语言 */}
            {
                role?.侧边栏?.语言?.visible && (
                    <div className="icons-icon-layout">
                        <div
                            className="right-icons-icon"
                            onClick={() => handleIcon(PANEL_TYPE.LANGUAGE)}
                        >
                            <img src={actionKey === PANEL_TYPE.LANGUAGE ? homeIconLanguageOpt : homeIconLanguage} alt="" className="icon-img" />
                            <div className={[`header-user ${actionKey === PANEL_TYPE.LANGUAGE ? 'weight' : ''}`]}>
                                {t('语言')}
                            </div>
                        </div>
                        <div className="panel-container">
                            <div className={[`user ${actionKey === PANEL_TYPE.LANGUAGE ? 'hide' : ''}`]}>
                                <div>
                                    <Select
                                        showSearch
                                        optionFilterProp="label"
                                        value={lang}
                                        style={{ width: 120 }}
                                        onChange={(val) => getInternationalizationInfo(
                                            val
                                        )}
                                        options={select.map((i) => ({
                                            ...i,
                                            label: t(i.label)
                                        }))}
                                    />
                                </div>
                            </div>
                            {
                                role?.侧边栏?.系统管理?.语言管理?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.LANGUAGE ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_LANG })}
                                    >
                                        <div>{t('语言管理')}</div>
                                    </div>
                                )
                            }
                        </div>
                    </div>
                )
            }

            {/* 系统管理 */}
            {
                role?.侧边栏?.系统管理 && (
                    <div className="icons-icon-layout">
                        <div
                            className="right-icons-icon"
                            onClick={() => handleIcon(PANEL_TYPE.SYSTEM_SET)}
                        >
                            <img src={actionKey === PANEL_TYPE.SYSTEM_SET ? homeIconSettingOpt : homeIconSetting} alt="" className="icon-img" />
                            <div className={[`header-user ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'weight' : ''}`]}>
                                {t('系统管理')}
                            </div>
                        </div>
                        <div className="panel-container">
                            {
                                role?.侧边栏?.系统管理?.单位管理?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_UNIT })}
                                    >
                                        <div>{t('单位管理')}</div>
                                    </div>
                                )
                            }
                            {
                                role?.侧边栏?.系统管理?.试样设置?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_SAMPLE })}
                                    >
                                        <div>{t('试样管理')}</div>
                                    </div>
                                )
                            }
                            {
                                role?.侧边栏?.系统管理?.试样设置?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_CONTROL_LIBRARY })}
                                    >
                                        <div>{t('子任务管理')}</div>
                                    </div>
                                )
                            }
                            {
                                role?.侧边栏?.系统管理?.点检?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_SPOT_CHECK })}
                                    >
                                        <div>{t('点检')}</div>
                                    </div>
                                )
                            }
                            {
                                role?.侧边栏?.系统管理?.首选项?.visible && (
                                    <div
                                        className={[`user  ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_SYSTEM_CONFIG_MODAL })}
                                    >
                                        <div>{t('系统目录设置')}</div>
                                    </div>
                                )
                            }
                            {
                                role?.侧边栏?.系统管理?.首选项?.visible && (
                                    <div
                                        className={[`user  ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_MASTER_SLAVE_MANAGER })}
                                    >
                                        <div>{t('主从管理器')}</div>
                                    </div>
                                )
                            }
                            {
                                role?.侧边栏?.系统管理?.首选项?.visible && (
                                    <div
                                        className={[`user  ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_APP_MANAGER })}
                                    >
                                        <div>{t('APP管理器')}</div>
                                    </div>
                                )
                            }
                            {
                                role?.侧边栏.软件配置.图片管理器?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_PICTURE })}
                                    >
                                        <div>{t('图片管理器')}</div>
                                    </div>
                                )
                            }
                            {
                                role?.侧边栏.软件配置.语音管理器?.visible && (
                                    <div
                                        className={[`user  ${actionKey === PANEL_TYPE.SYSTEM_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_AUDIO })}
                                    >
                                        <div>{t('语音管理器')}</div>
                                    </div>
                                )
                            }
                        </div>
                    </div>
                )
            }
            {/* 软件配置 */}
            {/* {
                role?.侧边栏.软件配置 && (
                    <div className="icons-icon-layout">
                        <div
                            className="right-icons-icon"
                            onClick={() => handleIcon(PANEL_TYPE.SOFTWARE_SET)}
                        >
                            <img src={actionKey === PANEL_TYPE.SOFTWARE_SET ? homeIconSystemOpt : homeIconSystem} alt="" className="icon-img" />
                            <div className={[`header-user ${actionKey === PANEL_TYPE.SOFTWARE_SET ? 'weight' : ''}`]}>
                                {t('软件配置')}
                            </div>
                        </div>
                        <div className="panel-container">
                            {
                                role?.侧边栏.软件配置.硬件管理器?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.SOFTWARE_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_HARDWARE })}
                                    >
                                        <div>
                                            {t('硬件管理器')}
                                        </div>
                                    </div>
                                )
                            }
                            {
                                role?.侧边栏.软件配置.侧边栏站管理器?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.SOFTWARE_SET ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_TOTAL_STATION })}
                                    >
                                        <div>
                                            {t('总站管理器')}
                                        </div>
                                    </div>
                                )
                            }
                        </div>
                    </div>
                )
            } */}
            {/* 信息 */}
            {
                role?.侧边栏.信息 && (
                    <div className="icons-icon-layout">
                        <div
                            className="right-icons-icon"
                            onClick={() => handleIcon(PANEL_TYPE.INFO)}
                        >
                            <img src={actionKey === PANEL_TYPE.INFO ? homeIconChatOpt : homeIconChat} alt="" className="icon-img" />
                            <div className={[`header-user ${actionKey === PANEL_TYPE.INFO ? 'weight' : ''}`]}>
                                {t('信息')}
                            </div>
                        </div>
                        <div className="panel-container">
                            {
                                role?.侧边栏.信息.日志?.visible && (
                                    <div
                                        className={[`user ${actionKey === PANEL_TYPE.INFO ? 'hide' : ''}`]}
                                        onClick={() => openDialog({ type: DIALOG_SYSTEM_LOG_MODAL })}
                                    >
                                        <div>{t('日志')}</div>
                                    </div>
                                )
                            }
                        </div>

                    </div>
                )
            }
            {/* 帮助 */}
            {
                role?.侧边栏.帮助?.visible && (
                    <div className="icons-icon-layout">
                        <div
                            id="helpBtn"
                            className="right-icons-icon"
                            onClick={() => openHelpDocByMdTag()}
                        >
                            <img src={actionKey === PANEL_TYPE.HELP ? homeIconHelpOpt : homeIconHelp} alt="" className="icon-img" />
                            <div className={[`header-user ${actionKey === PANEL_TYPE.HELP ? 'weight' : ''}`]}>
                                {t('帮助')}
                            </div>
                        </div>
                    </div>
                )
            }
            {showIcon && (
                <div className="icons-icon-bottom">
                    <div>
                        {t('版本号')} commit: 789414
                    </div>
                    <div>
                        {t('时间')} 2025-07-01 21:46
                    </div>
                </div>
            )}

        </RightSideBarContainer>
    )
}
export default RightSideBar
