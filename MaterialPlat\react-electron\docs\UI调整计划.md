# MaterialPlat React-Electron UI调整计划

## 项目概述

本项目是一个基于React + Electron的机械测试软件平台，采用现代化的前端技术栈，包括styled-components、Ant Design、Redux等。当前需要对整体UI进行系统性调整和优化。

## 当前UI架构分析

### 技术栈
- **UI框架**: React + Ant Design
- **样式方案**: styled-components + CSS-in-JS
- **状态管理**: Redux
- **图表库**: @arction/lcjs (LightningChart)
- **国际化**: react-i18next
- **响应式**: 基于vw单位的自适应设计

### 现有设计系统
- **颜色系统**: 定义在 `src/style/index.js`
- **响应式函数**: `rem()` 函数基于1920px基准
- **主题配置**: Ant Design主题定制
- **组件规范**: 统一的控件设计模式

## UI调整目标

### 1. 设计系统现代化
- 建立完整的设计令牌系统
- 统一颜色、字体、间距规范
- 优化视觉层次和信息架构

### 2. 用户体验优化
- 提升界面响应速度
- 优化交互流程
- 增强可访问性

### 3. 视觉风格升级
- 采用现代化设计语言
- 优化图标和插画系统
- 提升整体视觉质量

## 详细调整计划

### 阶段一：设计系统重构 (2-3周)

#### 1.1 颜色系统优化
**文件**: `src/style/index.js`

**当前问题**:
- 颜色定义分散，缺乏语义化
- 缺少暗色模式支持
- 品牌色彩体系不完整

**调整方案**:
```javascript
// 新的颜色系统
export const COLORS = {
  // 品牌色
  primary: {
    50: '#E3F2FD',
    100: '#BBDEFB',
    500: '#2196F3', // 主色
    600: '#1976D2',
    900: '#0D47A1'
  },
  // 功能色
  semantic: {
    success: '#52C41A',
    warning: '#FAAD14',
    error: '#FF4D4F',
    info: '#1890FF'
  },
  // 中性色
  neutral: {
    white: '#FFFFFF',
    gray50: '#FAFAFA',
    gray100: '#F5F5F5',
    gray900: '#262626',
    black: '#000000'
  }
}
```

#### 1.2 字体系统规范
**新增文件**: `src/style/typography.js`

```javascript
export const TYPOGRAPHY = {
  fontFamily: {
    primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    mono: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace'
  },
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '30px'
  },
  fontWeight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  }
}
```

#### 1.3 间距系统标准化
**文件**: `src/style/index.js`

```javascript
export const SPACING = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  '2xl': '48px',
  '3xl': '64px'
}
```

#### 1.4 styled-components全局变量和高级使用
**新增文件**: `src/style/theme.js`

**主题提供者配置**:
```javascript
import { createGlobalStyle, ThemeProvider } from 'styled-components'
import { COLORS, TYPOGRAPHY, SPACING } from './index'

// 全局主题对象
export const theme = {
  colors: COLORS,
  typography: TYPOGRAPHY,
  spacing: SPACING,
  breakpoints: {
    mobile: '768px',
    tablet: '1024px',
    desktop: '1440px',
    wide: '1920px'
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
  },
  borderRadius: {
    sm: '2px',
    md: '4px',
    lg: '8px',
    xl: '12px',
    full: '9999px'
  },
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal: 1040,
    popover: 1050,
    tooltip: 1060
  }
}

// 全局样式定义
export const GlobalStyle = createGlobalStyle`
  :root {
    /* CSS变量定义 */
    --color-primary: ${props => props.theme.colors.primary[500]};
    --color-primary-hover: ${props => props.theme.colors.primary[600]};
    --color-success: ${props => props.theme.colors.semantic.success};
    --color-warning: ${props => props.theme.colors.semantic.warning};
    --color-error: ${props => props.theme.colors.semantic.error};
    
    --font-family-primary: ${props => props.theme.typography.fontFamily.primary};
    --font-family-mono: ${props => props.theme.typography.fontFamily.mono};
    
    --spacing-xs: ${props => props.theme.spacing.xs};
    --spacing-sm: ${props => props.theme.spacing.sm};
    --spacing-md: ${props => props.theme.spacing.md};
    --spacing-lg: ${props => props.theme.spacing.lg};
    
    --border-radius-md: ${props => props.theme.borderRadius.md};
    --shadow-md: ${props => props.theme.shadows.md};
  }
  
  * {
    box-sizing: border-box;
  }
  
  body {
    font-family: var(--font-family-primary);
    color: ${props => props.theme.colors.neutral.gray900};
    background-color: ${props => props.theme.colors.neutral.gray50};
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: ${props => props.theme.colors.neutral.gray100};
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.neutral.gray900};
    border-radius: 4px;
    
    &:hover {
      background: ${props => props.theme.colors.primary[500]};
    }
  }
`

// 主题切换Hook
export const useTheme = () => {
  const [isDark, setIsDark] = useState(false)
  
  const toggleTheme = () => setIsDark(!isDark)
  
  const currentTheme = {
    ...theme,
    colors: isDark ? {
      ...theme.colors,
      neutral: {
        ...theme.colors.neutral,
        white: '#1a1a1a',
        gray50: '#262626',
        gray100: '#404040',
        gray900: '#ffffff',
        black: '#ffffff'
      }
    } : theme.colors
  }
  
  return { theme: currentTheme, isDark, toggleTheme }
}
```

**styled-components高级用法示例**:
```javascript
// 1. 主题感知组件
const StyledButton = styled.button`
  background-color: ${({ theme, variant }) => {
    switch (variant) {
      case 'primary': return theme.colors.primary[500]
      case 'success': return theme.colors.semantic.success
      case 'warning': return theme.colors.semantic.warning
      default: return theme.colors.neutral.gray100
    }
  }};
  
  color: ${({ theme, variant }) => 
    variant === 'primary' ? theme.colors.neutral.white : theme.colors.neutral.gray900
  };
  
  padding: ${({ theme, size }) => {
    switch (size) {
      case 'sm': return `${theme.spacing.xs} ${theme.spacing.sm}`
      case 'lg': return `${theme.spacing.md} ${theme.spacing.lg}`
      default: return `${theme.spacing.sm} ${theme.spacing.md}`
    }
  }};
  
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  
  /* 响应式设计 */
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    font-size: ${({ theme }) => theme.typography.fontSize.sm};
  }
  
  /* 状态变化 */
  &:hover {
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.md};
  }
  
  &:active {
    transform: translateY(0);
  }
  
  /* 禁用状态 */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`

// 2. 动态样式组件
const AnimatedCard = styled.div`
  background: ${({ theme }) => theme.colors.neutral.white};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  padding: ${({ theme }) => theme.spacing.lg};
  
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    box-shadow: ${({ theme }) => theme.shadows.xl};
    transform: translateY(-4px);
  }
`

// 3. 条件样式组件
const StatusIndicator = styled.div`
  width: 12px;
  height: 12px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  
  background-color: ${({ theme, status }) => {
    switch (status) {
      case 'online': return theme.colors.semantic.success
      case 'offline': return theme.colors.neutral.gray900
      case 'warning': return theme.colors.semantic.warning
      case 'error': return theme.colors.semantic.error
      default: return theme.colors.neutral.gray100
    }
  }};
  
  /* 动画效果 */
  ${({ status }) => status === 'online' && css`
    animation: pulse 2s infinite;
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
  `}
`

// 4. 复合组件样式
const FormGroup = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
  
  label {
    display: block;
    font-weight: ${({ theme }) => theme.typography.fontWeight.medium};
    color: ${({ theme }) => theme.colors.neutral.gray900};
    margin-bottom: ${({ theme }) => theme.spacing.xs};
  }
  
  input, select, textarea {
    width: 100%;
    padding: ${({ theme }) => theme.spacing.sm};
    border: 1px solid ${({ theme }) => theme.colors.neutral.gray100};
    border-radius: ${({ theme }) => theme.borderRadius.md};
    font-family: ${({ theme }) => theme.typography.fontFamily.primary};
    
    &:focus {
      outline: none;
      border-color: ${({ theme }) => theme.colors.primary[500]};
      box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[50]};
    }
    
    &:invalid {
      border-color: ${({ theme }) => theme.colors.semantic.error};
    }
  }
  
  .error-message {
    color: ${({ theme }) => theme.colors.semantic.error};
    font-size: ${({ theme }) => theme.typography.fontSize.sm};
    margin-top: ${({ theme }) => theme.spacing.xs};
  }
`
```

**应用入口配置**:
**文件**: `src/App.js`
```javascript
import { ThemeProvider } from 'styled-components'
import { theme, GlobalStyle } from './style/theme'

function App() {
  return (
    <ThemeProvider theme={theme}>
      <GlobalStyle />
      {/* 应用内容 */}
    </ThemeProvider>
  )
}
```

### 阶段二：组件库升级 (3-4周)

#### 2.1 基础组件重构

**优先级组件列表**:
1. **VButton** (`src/components/vButton/`)
   - 统一按钮样式和交互状态
   - 支持多种尺寸和变体
   - 优化加载和禁用状态

2. **VModal** (`src/components/vModal/`)
   - 重新设计弹窗样式
   - 优化动画效果
   - 统一头部和底部布局

3. **VTable** (`src/components/vTable/`)
   - 优化表格视觉设计
   - 改进排序和筛选交互
   - 增强响应式表现

4. **表单组件**
   - 统一表单项布局
   - 优化验证提示样式
   - 改进输入框和选择器设计

#### 2.2 控件系统优化
**目录**: `src/module/layout/controlComp/`

**调整重点**:
- 统一控件容器样式
- 优化配置界面设计
- 改进右键菜单交互
- 标准化设置抽屉组件

### 阶段三：页面布局优化 (2-3周)

#### 3.1 导航系统重构
**文件**: `src/components/navbar/`

**优化方向**:
- 简化导航层级
- 优化面包屑设计
- 改进用户状态显示
- 增强快捷操作入口

#### 3.2 首页布局升级
**文件**: `src/pages/sysHome/`

**调整内容**:
- 重新设计模块卡片
- 优化布局配置界面
- 改进状态栏设计
- 增强可定制性

#### 3.3 工作区界面优化
**文件**: `src/pages/layoutContent/`

**重点改进**:
- 优化分割布局交互
- 改进工具栏设计
- 增强拖拽体验
- 统一控件样式

### 阶段四：图表和数据可视化 (2-3周)

#### 4.1 图表组件升级
**文件**: `src/components/charts/`

**优化重点**:
- 统一图表主题样式
- 优化图例和工具提示
- 改进交互操作
- 增强响应式适配

#### 4.2 曲线控件优化
**文件**: `src/pages/layout/arrayCurve/`

**调整方向**:
- 重新设计配置界面
- 优化曲线样式选项
- 改进标注功能
- 增强性能表现

### 阶段五：对话框和弹窗系统 (2周)

#### 5.1 对话框统一化
**目录**: `src/pages/dialog/`

**标准化内容**:
- 统一对话框尺寸规范
- 标准化按钮布局
- 优化表单布局
- 改进加载状态

#### 5.2 系统管理界面
**重点页面**:
- 用户管理
- 角色管理
- 项目管理
- 模板管理

### 阶段六：响应式和性能优化 (1-2周)

#### 6.1 响应式设计改进
**文件**: `src/style/index.js`

**优化方案**:
- 重新设计断点系统
- 优化rem函数计算
- 改进移动端适配
- 增强触摸交互

#### 6.2 性能优化
- 组件懒加载优化
- 样式打包优化
- 图片资源优化
- 动画性能提升

## 实施计划

### 时间安排
- **总工期**: 12-15周
- **并行开发**: 设计系统和组件开发可并行进行
- **测试周期**: 每个阶段完成后进行1周测试

### 里程碑
1. **Week 3**: 设计系统重构完成
2. **Week 7**: 基础组件库升级完成
3. **Week 10**: 页面布局优化完成
4. **Week 13**: 图表系统升级完成
5. **Week 15**: 全面测试和优化完成

### 风险控制
- 保持向后兼容性
- 分阶段发布和测试
- 建立设计审查机制
- 制定回滚方案

## 预期效果

### 用户体验提升
- 界面响应速度提升30%
- 用户操作效率提升25%
- 视觉一致性达到95%

### 开发效率提升
- 组件复用率提升40%
- 新功能开发速度提升20%
- 维护成本降低30%

### 技术指标改善
- 包体积优化15%
- 首屏加载时间减少20%
- 内存使用优化10%

## 后续维护

### 设计系统文档
- 建立组件使用指南
- 制定设计规范文档
- 建立设计审查流程

### 持续优化
- 定期用户反馈收集
- 性能监控和优化
- 新技术跟进和应用

---

*本计划将根据实际开发进度和用户反馈进行动态调整*